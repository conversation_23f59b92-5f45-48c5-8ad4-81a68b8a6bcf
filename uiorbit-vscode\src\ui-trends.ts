/**
 * UIOrbit - Current UI/UX trends and patterns database
 * This file contains the latest UI trends that UIOrbit can apply to components
 */

export interface UITrend {
    name: string;
    description: string;
    category: 'visual' | 'interaction' | 'layout' | 'accessibility' | 'performance';
    popularity: 'emerging' | 'trending' | 'established' | 'declining';
    frameworks: string[];
    cssProperties?: string[];
    examples?: string[];
}

export const UI_TRENDS_2025: UITrend[] = [
    {
        name: "Glassmorphism",
        description: "Translucent glass-like effect with blur and transparency",
        category: "visual",
        popularity: "trending",
        frameworks: ["React", "Vue", "Angular", "Svelte"],
        cssProperties: ["backdrop-filter", "background", "border", "box-shadow"],
        examples: ["backdrop-filter: blur(10px)", "background: rgba(255, 255, 255, 0.1)"]
    },
    {
        name: "Neumorphism",
        description: "Soft, extruded plastic-like design with subtle shadows",
        category: "visual",
        popularity: "established",
        frameworks: ["React", "Vue", "Angular", "Svelte"],
        cssProperties: ["box-shadow", "border-radius", "background"],
        examples: ["box-shadow: 8px 8px 16px #d1d9e6, -8px -8px 16px #ffffff"]
    },
    {
        name: "Micro-interactions",
        description: "Small, delightful animations that provide feedback",
        category: "interaction",
        popularity: "trending",
        frameworks: ["React", "Vue", "Angular", "Svelte"],
        cssProperties: ["transition", "transform", "animation"],
        examples: ["transition: all 0.3s ease", "transform: scale(1.05)"]
    },
    {
        name: "Dark Mode First",
        description: "Designing primarily for dark themes with light mode as secondary",
        category: "visual",
        popularity: "trending",
        frameworks: ["React", "Vue", "Angular", "Svelte"],
        cssProperties: ["color-scheme", "prefers-color-scheme"],
        examples: ["@media (prefers-color-scheme: dark)"]
    },
    {
        name: "Brutalist Design",
        description: "Bold, raw, and intentionally rough aesthetic",
        category: "visual",
        popularity: "emerging",
        frameworks: ["React", "Vue", "Angular", "Svelte"],
        cssProperties: ["font-weight", "border", "background"],
        examples: ["font-weight: 900", "border: 4px solid black"]
    },
    {
        name: "Scroll-triggered Animations",
        description: "Animations that trigger based on scroll position",
        category: "interaction",
        popularity: "trending",
        frameworks: ["React", "Vue", "Angular", "Svelte"],
        cssProperties: ["transform", "opacity", "animation"],
        examples: ["Intersection Observer API", "CSS scroll-timeline"]
    },
    {
        name: "Asymmetrical Layouts",
        description: "Breaking grid systems for more dynamic compositions",
        category: "layout",
        popularity: "trending",
        frameworks: ["React", "Vue", "Angular", "Svelte"],
        cssProperties: ["grid", "flexbox", "position"],
        examples: ["grid-template-columns: 2fr 1fr", "justify-self: end"]
    },
    {
        name: "Voice UI Integration",
        description: "Adding voice commands and speech recognition",
        category: "accessibility",
        popularity: "emerging",
        frameworks: ["React", "Vue", "Angular", "Svelte"],
        examples: ["Web Speech API", "Voice commands"]
    },
    {
        name: "Progressive Web Apps",
        description: "App-like experiences in web browsers",
        category: "performance",
        popularity: "established",
        frameworks: ["React", "Vue", "Angular", "Svelte"],
        examples: ["Service Workers", "Web App Manifest"]
    },
    {
        name: "3D Elements",
        description: "Three-dimensional components and interactions",
        category: "visual",
        popularity: "emerging",
        frameworks: ["React", "Vue", "Angular", "Svelte"],
        cssProperties: ["transform-style", "perspective", "transform"],
        examples: ["transform-style: preserve-3d", "perspective: 1000px"]
    }
];

export class UITrendsService {
    static getTrendsByCategory(category: UITrend['category']): UITrend[] {
        return UI_TRENDS_2025.filter(trend => trend.category === category);
    }

    static getTrendsByPopularity(popularity: UITrend['popularity']): UITrend[] {
        return UI_TRENDS_2025.filter(trend => trend.popularity === popularity);
    }

    static getTrendsByFramework(framework: string): UITrend[] {
        return UI_TRENDS_2025.filter(trend => 
            trend.frameworks.includes(framework) || trend.frameworks.includes('All')
        );
    }

    static getRandomTrends(count: number = 3): UITrend[] {
        const shuffled = [...UI_TRENDS_2025].sort(() => 0.5 - Math.random());
        return shuffled.slice(0, count);
    }

    static getTrendingNow(): UITrend[] {
        return UI_TRENDS_2025.filter(trend => trend.popularity === 'trending');
    }

    static generateTrendPrompt(trends: UITrend[]): string {
        const trendDescriptions = trends.map(trend => 
            `- ${trend.name}: ${trend.description}`
        ).join('\n');

        return `Apply these current UI/UX trends to enhance the component:

${trendDescriptions}

Consider the following implementation approaches:
${trends.map(trend => trend.examples?.join(', ')).filter(Boolean).join('\n')}

Ensure the trends are applied tastefully and enhance rather than overwhelm the user experience.`;
    }
}
