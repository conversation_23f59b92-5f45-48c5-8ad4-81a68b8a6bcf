/**
 * UIOrbit - UI-specific prompt templates for different commands
 */

import { UITrendsService } from "./ui-trends";

export interface UIPromptContext {
  framework?: string;
  stylingFramework?: string;
  designSystem?: string;
  accessibilityLevel?: string;
  language?: string;
}

export class UIPromptTemplates {
  static generateComponent(context: UIPromptContext): string {
    return `🚀 Create a FUTURISTIC, cutting-edge UI component using ${
      context.framework || "the detected framework"
    }.

🎯 ADVANCED REQUIREMENTS:
- Use ${context.stylingFramework || "modern CSS"} with latest 2025 trends
- Apply ${
      context.designSystem || "modern design"
    } principles with innovative patterns
- Ensure ${context.accessibilityLevel || "WCAG AA"} accessibility compliance
- Make it fully responsive with fluid animations
- Include proper TypeScript types with advanced generics
- Add micro-interactions and delightful hover effects
- Use glassmorphism, neumorphism, or other trending visual styles
- Implement smooth transitions and modern animations
- Add dark mode support with elegant color schemes
- Include loading states and error handling
- Use modern CSS features (container queries, CSS Grid, Flexbox)
- Add helpful comments explaining design decisions

🌟 Generate a STUNNING, production-ready component that showcases the latest UI/UX trends for:`;
  }

  static addStyling(context: UIPromptContext): string {
    return `Enhance the following UI component with modern styling using ${
      context.stylingFramework || "CSS"
    }.

Requirements:
- Apply modern design trends and best practices
- Use a cohesive color scheme and typography
- Add smooth transitions and micro-interactions
- Ensure responsive design across all screen sizes
- Follow ${context.designSystem || "modern design"} system guidelines
- Maintain accessibility standards (${context.accessibilityLevel || "WCAG AA"})
- Use CSS custom properties for theming when appropriate

Enhance the styling for:`;
  }

  static makeResponsive(context: UIPromptContext): string {
    return `Make the following UI component fully responsive for mobile, tablet, and desktop devices.

Requirements:
- Use mobile-first approach
- Implement fluid layouts and flexible grids
- Add appropriate breakpoints for different screen sizes
- Optimize touch interactions for mobile devices
- Ensure text remains readable at all sizes
- Use relative units (rem, em, %, vw, vh) where appropriate
- Test layout behavior at common breakpoints (320px, 768px, 1024px, 1440px)

Make responsive:`;
  }

  static addAccessibility(context: UIPromptContext): string {
    return `Enhance the following UI component to meet ${
      context.accessibilityLevel || "WCAG AA"
    } accessibility standards.

Requirements:
- Add proper ARIA labels, roles, and properties
- Ensure keyboard navigation support
- Implement focus management and visible focus indicators
- Use semantic HTML elements
- Provide alternative text for images and icons
- Ensure sufficient color contrast ratios
- Add screen reader friendly descriptions
- Support high contrast mode and reduced motion preferences

Add accessibility features to:`;
  }

  static optimizeUI(context: UIPromptContext): string {
    return `Optimize the following UI component for performance and user experience.

Requirements:
- Minimize bundle size and improve loading performance
- Implement lazy loading where appropriate
- Optimize images and assets
- Reduce layout shifts and improve Core Web Vitals
- Add loading states and skeleton screens
- Implement error boundaries and fallback states
- Use efficient rendering patterns
- Add performance monitoring hooks if needed

Optimize:`;
  }

  static explainDesign(context: UIPromptContext): string {
    return `Analyze and explain the design patterns, UI principles, and architectural decisions in the following component.

Please cover:
- Design patterns and principles used
- Component architecture and structure
- Accessibility considerations
- Responsive design approach
- Performance implications
- Best practices demonstrated
- Potential improvements or alternatives
- How it fits within ${context.designSystem || "modern design"} systems

Explain the design of:`;
  }

  static generateVariants(context: UIPromptContext): string {
    return `Create multiple design variants of the following UI component.

Generate 3-4 different variants with:
- Different visual styles (minimal, bold, elegant, playful)
- Various size options (compact, default, large)
- Different color schemes and themes
- Alternative layouts and arrangements
- Maintain the same functionality and accessibility
- Use ${context.stylingFramework || "modern CSS"} for styling
- Follow ${context.designSystem || "design"} principles

Create variants for:`;
  }

  static trendingPatterns(context: UIPromptContext): string {
    const trendingNow = UITrendsService.getTrendingNow();
    const frameworkTrends = context.framework
      ? UITrendsService.getTrendsByFramework(context.framework)
      : [];

    const relevantTrends = [...new Set([...trendingNow, ...frameworkTrends])];
    const trendPrompt = UITrendsService.generateTrendPrompt(
      relevantTrends.slice(0, 5)
    );

    return `Apply current UI/UX trends and modern design patterns to the following component.

${trendPrompt}

Additional considerations:
- Ensure trends enhance usability rather than just aesthetics
- Maintain accessibility standards while applying visual trends
- Consider performance implications of animations and effects
- Test across different devices and screen sizes

Apply trending patterns to:`;
  }

  static designSystem(context: UIPromptContext): string {
    return `Apply ${
      context.designSystem || "modern design system"
    } principles to the following component.

Requirements:
- Use consistent spacing, typography, and color tokens
- Follow the design system's component patterns
- Implement proper component variants and states
- Use design tokens for theming and customization
- Ensure consistency with other system components
- Add proper documentation and usage examples
- Include component props and API design
- Follow naming conventions and file structure

Apply design system principles to:`;
  }

  static customUIPrompt(
    context: UIPromptContext,
    customPrompt: string
  ): string {
    return `${customPrompt}

Context:
- Framework: ${context.framework || "Auto-detect"}
- Styling: ${context.stylingFramework || "Auto-detect"}
- Design System: ${context.designSystem || "None"}
- Accessibility: ${context.accessibilityLevel || "WCAG AA"}

Please ensure the solution follows modern UI/UX best practices and is production-ready.

Apply to:`;
  }
}
