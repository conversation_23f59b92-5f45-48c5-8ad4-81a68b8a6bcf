/**
 * UIOrbit Live Preview System - Real-time component preview like Lovable.dev
 * Provides instant visual feedback for generated components
 */

import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export interface PreviewConfig {
    framework: string;
    port: number;
    autoReload: boolean;
    showInSidebar: boolean;
}

export class LivePreviewManager {
    private panel: vscode.WebviewPanel | undefined;
    private devServer: any;
    private workspaceRoot: string;
    private previewPort: number = 3000;
    private hotReloadEnabled: boolean = true;
    private fileWatcher: vscode.FileSystemWatcher | undefined;

    constructor(workspaceRoot: string) {
        this.workspaceRoot = workspaceRoot;
        this.setupHotReload();
    }

    /**
     * Create or show the live preview panel
     */
    async showPreview(componentCode: string, framework: string = 'react'): Promise<void> {
        if (this.panel) {
            this.panel.reveal(vscode.ViewColumn.Beside);
        } else {
            this.panel = vscode.window.createWebviewPanel(
                'uiorbitPreview',
                '🚀 UIOrbit Live Preview',
                vscode.ViewColumn.Beside,
                {
                    enableScripts: true,
                    retainContextWhenHidden: true,
                    localResourceRoots: [vscode.Uri.file(this.workspaceRoot)]
                }
            );

            this.panel.onDidDispose(() => {
                this.panel = undefined;
                this.stopDevServer();
            });
        }

        // Generate preview HTML based on framework
        const previewHtml = await this.generatePreviewHtml(componentCode, framework);
        this.panel.webview.html = previewHtml;

        // Start dev server if needed
        await this.startDevServer(framework);
    }

    /**
     * Generate HTML for component preview
     */
    private async generatePreviewHtml(componentCode: string, framework: string): Promise<string> {
        switch (framework.toLowerCase()) {
            case 'react':
                return this.generateReactPreview(componentCode);
            case 'vue':
                return this.generateVuePreview(componentCode);
            case 'angular':
                return this.generateAngularPreview(componentCode);
            default:
                return this.generateReactPreview(componentCode);
        }
    }

    private generateReactPreview(componentCode: string): string {
        return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UIOrbit Preview</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .preview-container {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            max-width: 1200px;
            margin: 0 auto;
        }
        .preview-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
        }
        .preview-badge {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        .error-container {
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 8px;
            padding: 16px;
            color: #dc2626;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
        }
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 200px;
            color: #6b7280;
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <div class="preview-header">
            <div class="preview-badge">🚀 UIOrbit Live Preview</div>
            <div style="color: #6b7280; font-size: 14px;">React Component</div>
        </div>
        <div id="preview-root">
            <div class="loading">Loading component...</div>
        </div>
    </div>

    <script type="text/babel">
        try {
            // Component code will be injected here
            ${componentCode}
            
            // Render the component
            const root = ReactDOM.createRoot(document.getElementById('preview-root'));
            
            // Try to find the main component export
            let ComponentToRender;
            if (typeof Component !== 'undefined') {
                ComponentToRender = Component;
            } else if (typeof App !== 'undefined') {
                ComponentToRender = App;
            } else {
                // Try to find any React component in the code
                const componentMatch = componentCode.match(/(?:export\\s+(?:default\\s+)?(?:function|const)\\s+)(\\w+)/);
                if (componentMatch) {
                    ComponentToRender = window[componentMatch[1]];
                }
            }
            
            if (ComponentToRender) {
                root.render(React.createElement(ComponentToRender));
            } else {
                root.render(React.createElement('div', {
                    className: 'error-container'
                }, 'Could not find a React component to render. Make sure your component is exported.'));
            }
        } catch (error) {
            console.error('Preview error:', error);
            const root = ReactDOM.createRoot(document.getElementById('preview-root'));
            root.render(React.createElement('div', {
                className: 'error-container'
            }, 'Error rendering component: ' + error.message));
        }
    </script>
</body>
</html>`;
    }

    private generateVuePreview(componentCode: string): string {
        return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UIOrbit Vue Preview</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .preview-container {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            max-width: 1200px;
            margin: 0 auto;
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 24px;">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 600;">
                🚀 UIOrbit Live Preview
            </div>
            <div style="color: #6b7280; font-size: 14px;">Vue Component</div>
        </div>
        <div id="preview-root"></div>
    </div>

    <script>
        try {
            ${componentCode}
            
            const { createApp } = Vue;
            createApp(App || Component).mount('#preview-root');
        } catch (error) {
            console.error('Preview error:', error);
            document.getElementById('preview-root').innerHTML = 
                '<div style="background: #fef2f2; border: 1px solid #fecaca; border-radius: 8px; padding: 16px; color: #dc2626;">Error: ' + error.message + '</div>';
        }
    </script>
</body>
</html>`;
    }

    private generateAngularPreview(componentCode: string): string {
        return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UIOrbit Angular Preview</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .preview-container {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            max-width: 1200px;
            margin: 0 auto;
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 24px;">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 600;">
                🚀 UIOrbit Live Preview
            </div>
            <div style="color: #6b7280; font-size: 14px;">Angular Component</div>
        </div>
        <div id="preview-root">
            <div style="text-align: center; padding: 40px; color: #6b7280;">
                Angular preview requires a development server.<br>
                <button onclick="startDevServer()" style="margin-top: 16px; background: #3b82f6; color: white; padding: 8px 16px; border: none; border-radius: 6px; cursor: pointer;">
                    Start Dev Server
                </button>
            </div>
        </div>
    </div>

    <script>
        function startDevServer() {
            // This would trigger the dev server start
            document.getElementById('preview-root').innerHTML = 
                '<div style="text-align: center; padding: 40px; color: #059669;">Starting Angular dev server...</div>';
        }
    </script>
</body>
</html>`;
    }

    /**
     * Start development server for hot reload
     */
    private async startDevServer(framework: string): Promise<void> {
        if (this.devServer) return;

        try {
            const packageJsonPath = path.join(this.workspaceRoot, 'package.json');
            if (!fs.existsSync(packageJsonPath)) return;

            const commands: Record<string, string> = {
                'react': 'npm start',
                'next': 'npm run dev',
                'vue': 'npm run dev',
                'angular': 'ng serve',
                'svelte': 'npm run dev'
            };

            const command = commands[framework.toLowerCase()];
            if (command) {
                // Start dev server in background
                this.devServer = exec(command, { cwd: this.workspaceRoot });
                
                vscode.window.showInformationMessage(
                    `🚀 Starting ${framework} dev server on port ${this.previewPort}...`
                );
            }
        } catch (error) {
            console.error('Failed to start dev server:', error);
        }
    }

    /**
     * Stop the development server
     */
    private stopDevServer(): void {
        if (this.devServer) {
            this.devServer.kill();
            this.devServer = undefined;
        }
    }

    /**
     * Update preview with new component code
     */
    async updatePreview(componentCode: string, framework: string = 'react'): Promise<void> {
        if (this.panel) {
            const previewHtml = await this.generatePreviewHtml(componentCode, framework);
            this.panel.webview.html = previewHtml;
        }
    }

    /**
     * Setup hot reload functionality
     */
    private setupHotReload(): void {
        if (this.hotReloadEnabled) {
            // Watch for file changes in src directory
            const srcPattern = new vscode.RelativePattern(this.workspaceRoot, 'src/**/*.{ts,tsx,js,jsx,vue,css,scss}');
            this.fileWatcher = vscode.workspace.createFileSystemWatcher(srcPattern);

            this.fileWatcher.onDidChange(uri => {
                this.handleFileChange(uri);
            });

            this.fileWatcher.onDidCreate(uri => {
                this.handleFileChange(uri);
            });

            this.fileWatcher.onDidDelete(uri => {
                this.handleFileChange(uri);
            });
        }
    }

    /**
     * Handle file changes for hot reload
     */
    private async handleFileChange(uri: vscode.Uri): Promise<void> {
        if (this.panel && this.hotReloadEnabled) {
            const filePath = uri.fsPath;
            const fileName = path.basename(filePath);

            // Show notification about file change
            vscode.window.showInformationMessage(
                `🔄 Hot reload: ${fileName} updated`,
                { modal: false }
            );

            // If it's a component file, try to update the preview
            if (this.isComponentFile(filePath)) {
                try {
                    const content = fs.readFileSync(filePath, 'utf8');
                    await this.updatePreview(content, this.detectFrameworkFromFile(filePath));
                } catch (error) {
                    console.error('Hot reload failed:', error);
                }
            }
        }
    }

    /**
     * Check if file is a component file
     */
    private isComponentFile(filePath: string): boolean {
        const componentExtensions = ['.tsx', '.jsx', '.vue'];
        return componentExtensions.some(ext => filePath.endsWith(ext));
    }

    /**
     * Detect framework from file extension
     */
    private detectFrameworkFromFile(filePath: string): string {
        if (filePath.endsWith('.vue')) return 'vue';
        if (filePath.endsWith('.tsx') || filePath.endsWith('.jsx')) return 'react';
        return 'react';
    }

    /**
     * Enable/disable hot reload
     */
    setHotReload(enabled: boolean): void {
        this.hotReloadEnabled = enabled;
        if (enabled && !this.fileWatcher) {
            this.setupHotReload();
        } else if (!enabled && this.fileWatcher) {
            this.fileWatcher.dispose();
            this.fileWatcher = undefined;
        }
    }

    /**
     * Get hot reload status
     */
    isHotReloadEnabled(): boolean {
        return this.hotReloadEnabled;
    }

    /**
     * Dispose of the preview panel and cleanup
     */
    dispose(): void {
        if (this.panel) {
            this.panel.dispose();
        }
        if (this.fileWatcher) {
            this.fileWatcher.dispose();
        }
        this.stopDevServer();
    }
}
