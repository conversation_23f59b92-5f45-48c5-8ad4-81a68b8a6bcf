/**
 * UIOrbit - Local Vector Database for UI patterns and context storage
 * This provides a simple in-memory vector database for storing and retrieving UI patterns
 */

import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';

export interface UIPattern {
    id: string;
    name: string;
    description: string;
    framework: string;
    category: 'component' | 'layout' | 'styling' | 'interaction' | 'accessibility';
    tags: string[];
    code: string;
    embedding?: number[];
    usage_count: number;
    created_at: Date;
    updated_at: Date;
}

export interface ProjectContext {
    id: string;
    file_path: string;
    content: string;
    language: string;
    framework?: string;
    dependencies: string[];
    imports: string[];
    exports: string[];
    components: string[];
    embedding?: number[];
    last_modified: Date;
}

export class VectorDatabase {
    private patterns: Map<string, UIPattern> = new Map();
    private contexts: Map<string, ProjectContext> = new Map();
    private dbPath: string;

    constructor(private extensionContext: vscode.ExtensionContext) {
        this.dbPath = path.join(extensionContext.globalStorageUri.fsPath, 'uiorbit-db.json');
        this.loadDatabase();
        this.initializeDefaultPatterns();
    }

    private async loadDatabase(): Promise<void> {
        try {
            if (fs.existsSync(this.dbPath)) {
                const data = JSON.parse(fs.readFileSync(this.dbPath, 'utf8'));
                
                if (data.patterns) {
                    for (const pattern of data.patterns) {
                        this.patterns.set(pattern.id, {
                            ...pattern,
                            created_at: new Date(pattern.created_at),
                            updated_at: new Date(pattern.updated_at)
                        });
                    }
                }

                if (data.contexts) {
                    for (const context of data.contexts) {
                        this.contexts.set(context.id, {
                            ...context,
                            last_modified: new Date(context.last_modified)
                        });
                    }
                }
            }
        } catch (error) {
            console.error('Failed to load UIOrbit database:', error);
        }
    }

    private async saveDatabase(): Promise<void> {
        try {
            // Ensure directory exists
            const dir = path.dirname(this.dbPath);
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }

            const data = {
                patterns: Array.from(this.patterns.values()),
                contexts: Array.from(this.contexts.values()),
                version: '1.0.0',
                last_updated: new Date().toISOString()
            };

            fs.writeFileSync(this.dbPath, JSON.stringify(data, null, 2));
        } catch (error) {
            console.error('Failed to save UIOrbit database:', error);
        }
    }

    private initializeDefaultPatterns(): void {
        const defaultPatterns: Omit<UIPattern, 'id' | 'created_at' | 'updated_at'>[] = [
            {
                name: "React Button Component",
                description: "Modern, accessible button component with variants",
                framework: "React",
                category: "component",
                tags: ["button", "interactive", "accessible"],
                code: `interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  children: React.ReactNode;
  onClick?: () => void;
}

export const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  disabled = false,
  children,
  onClick
}) => {
  const baseClasses = 'font-medium rounded-lg transition-colors focus:outline-none focus:ring-2';
  const variantClasses = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',
    secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',
    outline: 'border-2 border-blue-600 text-blue-600 hover:bg-blue-50 focus:ring-blue-500'
  };
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg'
  };

  return (
    <button
      className={\`\${baseClasses} \${variantClasses[variant]} \${sizeClasses[size]} \${disabled ? 'opacity-50 cursor-not-allowed' : ''}\`}
      disabled={disabled}
      onClick={onClick}
      aria-disabled={disabled}
    >
      {children}
    </button>
  );
};`,
                usage_count: 0
            },
            {
                name: "Responsive Card Layout",
                description: "Flexible card component with responsive design",
                framework: "React",
                category: "layout",
                tags: ["card", "responsive", "layout"],
                code: `interface CardProps {
  title?: string;
  children: React.ReactNode;
  className?: string;
}

export const Card: React.FC<CardProps> = ({ title, children, className = '' }) => {
  return (
    <div className={\`bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden \${className}\`}>
      {title && (
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
        </div>
      )}
      <div className="p-6">
        {children}
      </div>
    </div>
  );
};`,
                usage_count: 0
            }
        ];

        for (const pattern of defaultPatterns) {
            const id = this.generateId();
            const now = new Date();
            this.patterns.set(id, {
                ...pattern,
                id,
                created_at: now,
                updated_at: now
            });
        }

        this.saveDatabase();
    }

    public addPattern(pattern: Omit<UIPattern, 'id' | 'created_at' | 'updated_at'>): string {
        const id = this.generateId();
        const now = new Date();
        
        const newPattern: UIPattern = {
            ...pattern,
            id,
            created_at: now,
            updated_at: now
        };

        this.patterns.set(id, newPattern);
        this.saveDatabase();
        return id;
    }

    public getPattern(id: string): UIPattern | undefined {
        return this.patterns.get(id);
    }

    public searchPatterns(query: string, framework?: string, category?: string): UIPattern[] {
        const results: UIPattern[] = [];
        const queryLower = query.toLowerCase();

        for (const pattern of this.patterns.values()) {
            // Filter by framework if specified
            if (framework && pattern.framework !== framework) {
                continue;
            }

            // Filter by category if specified
            if (category && pattern.category !== category) {
                continue;
            }

            // Search in name, description, and tags
            const searchText = `${pattern.name} ${pattern.description} ${pattern.tags.join(' ')}`.toLowerCase();
            if (searchText.includes(queryLower)) {
                results.push(pattern);
            }
        }

        // Sort by usage count (most used first)
        return results.sort((a, b) => b.usage_count - a.usage_count);
    }

    public addProjectContext(context: Omit<ProjectContext, 'id'>): string {
        const id = this.generateId();
        const newContext: ProjectContext = {
            ...context,
            id
        };

        this.contexts.set(id, newContext);
        this.saveDatabase();
        return id;
    }

    public getProjectContext(filePath: string): ProjectContext | undefined {
        for (const context of this.contexts.values()) {
            if (context.file_path === filePath) {
                return context;
            }
        }
        return undefined;
    }

    public updatePatternUsage(id: string): void {
        const pattern = this.patterns.get(id);
        if (pattern) {
            pattern.usage_count++;
            pattern.updated_at = new Date();
            this.saveDatabase();
        }
    }

    public getPopularPatterns(limit: number = 10): UIPattern[] {
        return Array.from(this.patterns.values())
            .sort((a, b) => b.usage_count - a.usage_count)
            .slice(0, limit);
    }

    public getPatternsByFramework(framework: string): UIPattern[] {
        return Array.from(this.patterns.values())
            .filter(pattern => pattern.framework === framework);
    }

    private generateId(): string {
        return Math.random().toString(36).substr(2, 9);
    }

    public cleanup(): void {
        // Remove old contexts (older than 30 days)
        const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        
        for (const [id, context] of this.contexts.entries()) {
            if (context.last_modified < thirtyDaysAgo) {
                this.contexts.delete(id);
            }
        }

        this.saveDatabase();
    }
}
