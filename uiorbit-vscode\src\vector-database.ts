/**
 * UIOrbit - Local Vector Database for UI patterns and context storage
 * Augment Code-style local-first vector database with SQLite + vector extensions
 * Privacy-first: All user code processing happens locally
 */

import * as vscode from 'vscode';

import * as fs from 'fs';

import * as path from 'path';

import Database from 'better-sqlite3';

import { loadVss } from 'sqlite-vss';

export interface UIPattern {
    id: string;
    name: string;
    description: string;
    framework: string;
    category: 'component' | 'layout' | 'styling' | 'interaction' | 'accessibility' | 'animation';
    tags: string[];
    code: string;
    embedding: Float32Array;
    usageCount: number;
    trendScore: number;
    accessibilityScore: number;
    performanceScore: number;
    createdAt: Date;
    updatedAt: Date;
}

export interface ProjectContext {
    id: string;
    filePath: string;
    content: string;
    language: string;
    framework?: string;
    dependencies: string[];
    imports: string[];
    exports: string[];
    components: string[];
    embedding: Float32Array;
    semanticChunks: SemanticChunk[];
    lastModified: Date;
}

export interface SemanticChunk {
    id: string;
    type: 'component' | 'hook' | 'utility' | 'style' | 'type';
    name: string;
    content: string;
    startLine: number;
    endLine: number;
    embedding: Float32Array;
    dependencies: string[];
}

export interface SearchResult {
    item: UIPattern | ProjectContext;
    similarity: number;
    type: 'pattern' | 'context';
}

export class VectorDatabase {
    private db: any; // Database instance
    private patterns: Map<string, UIPattern> = new Map();
    private contexts: Map<string, ProjectContext> = new Map();
    private dbPath: string;
    private isInitialized: boolean = false;

    constructor(private extensionContext: vscode.ExtensionContext) {
        this.dbPath = path.join(extensionContext.globalStorageUri.fsPath, 'uiorbit.db');
        this.initializeDatabase();
    }

    private async initializeDatabase(): Promise<void> {
        try {
            // For now, use JSON fallback until SQLite is properly installed
            await this.loadDatabase();
            this.initializeDefaultPatterns();
            this.isInitialized = true;
        } catch (error) {
            console.error('Failed to initialize UIOrbit database:', error);
            // Fallback to in-memory storage
            this.isInitialized = true;
        }
    }

    private async loadDatabase(): Promise<void> {
        try {
            if (fs.existsSync(this.dbPath)) {
                const data = JSON.parse(fs.readFileSync(this.dbPath, 'utf8'));
                
                if (data.patterns) {
                    for (const pattern of data.patterns) {
                        this.patterns.set(pattern.id, {
                            ...pattern,
                            created_at: new Date(pattern.created_at),
                            updated_at: new Date(pattern.updated_at)
                        });
                    }
                }

                if (data.contexts) {
                    for (const context of data.contexts) {
                        this.contexts.set(context.id, {
                            ...context,
                            last_modified: new Date(context.last_modified)
                        });
                    }
                }
            }
        } catch (error) {
            console.error('Failed to load UIOrbit database:', error);
        }
    }

    private async saveDatabase(): Promise<void> {
        try {
            // Ensure directory exists
            const dir = path.dirname(this.dbPath);
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }

            const data = {
                patterns: Array.from(this.patterns.values()),
                contexts: Array.from(this.contexts.values()),
                version: '1.0.0',
                last_updated: new Date().toISOString()
            };

            fs.writeFileSync(this.dbPath, JSON.stringify(data, null, 2));
        } catch (error) {
            console.error('Failed to save UIOrbit database:', error);
        }
    }

    private initializeDefaultPatterns(): void {
        const defaultPatterns: Omit<UIPattern, 'id' | 'createdAt' | 'updatedAt'>[] = [
            {
                name: "React Button Component",
                description: "Modern, accessible button component with variants",
                framework: "React",
                category: "component",
                tags: ["button", "interactive", "accessible"],
                code: `interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  children: React.ReactNode;
  onClick?: () => void;
}

export const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  disabled = false,
  children,
  onClick
}) => {
  const baseClasses = 'font-medium rounded-lg transition-colors focus:outline-none focus:ring-2';
  const variantClasses = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',
    secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',
    outline: 'border-2 border-blue-600 text-blue-600 hover:bg-blue-50 focus:ring-blue-500'
  };
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg'
  };

  return (
    <button
      className={\`\${baseClasses} \${variantClasses[variant]} \${sizeClasses[size]} \${disabled ? 'opacity-50 cursor-not-allowed' : ''}\`}
      disabled={disabled}
      onClick={onClick}
      aria-disabled={disabled}
    >
      {children}
    </button>
  );
};`,
                usageCount: 0,
                embedding: new Float32Array(384), // Default embedding size
                trendScore: 0.8,
                accessibilityScore: 0.9,
                performanceScore: 0.85
            },
            {
                name: "Responsive Card Layout",
                description: "Flexible card component with responsive design",
                framework: "React",
                category: "layout",
                tags: ["card", "responsive", "layout"],
                code: `interface CardProps {
  title?: string;
  children: React.ReactNode;
  className?: string;
}

export const Card: React.FC<CardProps> = ({ title, children, className = '' }) => {
  return (
    <div className={\`bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden \${className}\`}>
      {title && (
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
        </div>
      )}
      <div className="p-6">
        {children}
      </div>
    </div>
  );
};`,
                usageCount: 0,
                embedding: new Float32Array(384),
                trendScore: 0.7,
                accessibilityScore: 0.8,
                performanceScore: 0.9
            }
        ];

        for (const pattern of defaultPatterns) {
            const id = this.generateId();
            const now = new Date();
            this.patterns.set(id, {
                ...pattern,
                id,
                createdAt: now,
                updatedAt: now
            });
        }

        this.saveDatabase();
    }

    public addPattern(pattern: Omit<UIPattern, 'id' | 'createdAt' | 'updatedAt'>): string {
        const id = this.generateId();
        const now = new Date();

        const newPattern: UIPattern = {
            ...pattern,
            id,
            createdAt: now,
            updatedAt: now
        };

        this.patterns.set(id, newPattern);
        this.saveDatabase();
        return id;
    }

    public getPattern(id: string): UIPattern | undefined {
        return this.patterns.get(id);
    }

    public searchPatterns(query: string, framework?: string, category?: string): UIPattern[] {
        const results: UIPattern[] = [];
        const queryLower = query.toLowerCase();

        for (const pattern of this.patterns.values()) {
            // Filter by framework if specified
            if (framework && pattern.framework !== framework) {
                continue;
            }

            // Filter by category if specified
            if (category && pattern.category !== category) {
                continue;
            }

            // Search in name, description, and tags
            const searchText = `${pattern.name} ${pattern.description} ${pattern.tags.join(' ')}`.toLowerCase();
            if (searchText.includes(queryLower)) {
                results.push(pattern);
            }
        }

        // Sort by usage count (most used first)
        return results.sort((a, b) => b.usageCount - a.usageCount);
    }

    public addProjectContext(context: Omit<ProjectContext, 'id'>): string {
        const id = this.generateId();
        const newContext: ProjectContext = {
            ...context,
            id
        };

        this.contexts.set(id, newContext);
        this.saveDatabase();
        return id;
    }

    public getProjectContext(filePath: string): ProjectContext | undefined {
        for (const context of this.contexts.values()) {
            if (context.filePath === filePath) {
                return context;
            }
        }
        return undefined;
    }

    public updatePatternUsage(id: string): void {
        const pattern = this.patterns.get(id);
        if (pattern) {
            pattern.usageCount++;
            pattern.updatedAt = new Date();
            this.saveDatabase();
        }
    }

    public getPopularPatterns(limit: number = 10): UIPattern[] {
        return Array.from(this.patterns.values())
            .sort((a, b) => b.usageCount - a.usageCount)
            .slice(0, limit);
    }

    public getPatternsByFramework(framework: string): UIPattern[] {
        return Array.from(this.patterns.values())
            .filter(pattern => pattern.framework === framework);
    }

    private generateId(): string {
        return Math.random().toString(36).substring(2, 11);
    }

    public cleanup(): void {
        // Remove old contexts (older than 30 days)
        const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

        for (const [id, context] of this.contexts.entries()) {
            if (context.lastModified < thirtyDaysAgo) {
                this.contexts.delete(id);
            }
        }

        this.saveDatabase();
    }

    // Enhanced methods for Augment Code-style functionality
    public async semanticSearch(query: string, limit: number = 10): Promise<SearchResult[]> {
        // For now, use simple text matching until we implement proper embeddings
        const results: SearchResult[] = [];

        // Search patterns
        for (const pattern of this.patterns.values()) {
            const searchText = `${pattern.name} ${pattern.description} ${pattern.tags.join(' ')} ${pattern.code}`.toLowerCase();
            if (searchText.includes(query.toLowerCase())) {
                results.push({
                    item: pattern,
                    similarity: this.calculateTextSimilarity(query, searchText),
                    type: 'pattern'
                });
            }
        }

        // Search project contexts
        for (const context of this.contexts.values()) {
            const searchText = `${context.filePath} ${context.content} ${context.components.join(' ')}`.toLowerCase();
            if (searchText.includes(query.toLowerCase())) {
                results.push({
                    item: context,
                    similarity: this.calculateTextSimilarity(query, searchText),
                    type: 'context'
                });
            }
        }

        return results
            .sort((a, b) => b.similarity - a.similarity)
            .slice(0, limit);
    }

    public getRelevantContext(filePath: string, _query: string): ProjectContext[] {
        const contexts: ProjectContext[] = [];
        const currentDir = path.dirname(filePath);

        for (const context of this.contexts.values()) {
            const contextDir = path.dirname(context.filePath);

            // Prioritize files in the same directory
            if (contextDir === currentDir) {
                contexts.push(context);
            }
            // Include related files based on imports/exports
            else if (this.areFilesRelated(filePath, context.filePath)) {
                contexts.push(context);
            }
        }

        return contexts.slice(0, 5); // Limit to 5 most relevant contexts
    }

    private calculateTextSimilarity(query: string, text: string): number {
        const queryWords = query.toLowerCase().split(/\s+/);
        const textWords = text.toLowerCase().split(/\s+/);

        let matches = 0;
        for (const word of queryWords) {
            if (textWords.some(textWord => textWord.includes(word))) {
                matches++;
            }
        }

        return matches / queryWords.length;
    }

    private areFilesRelated(file1: string, file2: string): boolean {
        const context1 = this.getProjectContext(file1);
        const context2 = this.getProjectContext(file2);

        if (!context1 || !context2) {
            return false;
        }

        // Check if they share imports/exports
        const sharedDeps = context1.dependencies.filter(dep =>
            context2.dependencies.includes(dep)
        );

        return sharedDeps.length > 0;
    }

    public getTrendingPatterns(): UIPattern[] {
        return Array.from(this.patterns.values())
            .sort((a, b) => b.trendScore - a.trendScore)
            .slice(0, 10);
    }

    public getAccessiblePatterns(): UIPattern[] {
        return Array.from(this.patterns.values())
            .filter(pattern => pattern.accessibilityScore > 0.7)
            .sort((a, b) => b.accessibilityScore - a.accessibilityScore);
    }

    public getPerformantPatterns(): UIPattern[] {
        return Array.from(this.patterns.values())
            .filter(pattern => pattern.performanceScore > 0.8)
            .sort((a, b) => b.performanceScore - a.performanceScore);
    }
}
