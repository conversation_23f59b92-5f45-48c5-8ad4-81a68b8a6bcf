/**
 * UIOrbit - Clean Augment Code-style View Provider
 * Privacy-first UI/UX AI assistant with local codebase intelligence
 */

import * as vscode from 'vscode';
import { VectorDatabase } from './vector-database';
import { FileWatcher } from './file-watcher';
import { ASTAnalyzer } from './ast-analyzer';

export interface UIOrbitMessage {
    type: 'request' | 'response' | 'error' | 'status';
    content: string;
    timestamp: Date;
    context?: any;
}

export class CleanUIOrbitViewProvider implements vscode.WebviewViewProvider {
    private webView?: vscode.WebviewView;
    private vectorDb: VectorDatabase;
    private fileWatcher: FileWatcher;
    private astAnalyzer: ASTAnalyzer;
    private messages: UIOrbitMessage[] = [];
    private isProcessing: boolean = false;

    constructor(private context: vscode.ExtensionContext) {
        this.vectorDb = new VectorDatabase(context);
        this.fileWatcher = new FileWatcher(context, this.vectorDb);
        this.astAnalyzer = new ASTAnalyzer();
    }

    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        _context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken
    ): void {
        this.webView = webviewView;

        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [this.context.extensionUri]
        };

        webviewView.webview.html = this.getWebviewContent();

        // Handle messages from webview
        webviewView.webview.onDidReceiveMessage(async (message) => {
            await this.handleWebviewMessage(message);
        });

        // Start file watching for real-time codebase intelligence
        this.initializeServices();
    }

    private async initializeServices(): Promise<void> {
        try {
            // Initialize file watcher for real-time indexing
            await this.fileWatcher.start();
            
            this.addMessage({
                type: 'status',
                content: '🚀 UIOrbit initialized - Local codebase intelligence active',
                timestamp: new Date()
            });
        } catch (error) {
            this.addMessage({
                type: 'error',
                content: `Failed to initialize UIOrbit: ${error}`,
                timestamp: new Date()
            });
        }
    }

    private async handleWebviewMessage(message: any): Promise<void> {
        switch (message.type) {
            case 'ui-request':
                await this.handleUIRequest(message.content);
                break;
            case 'get-context':
                await this.sendProjectContext();
                break;
            case 'clear-conversation':
                this.clearConversation();
                break;
            default:
                console.log('Unknown message type:', message.type);
        }
    }

    private async handleUIRequest(prompt: string): Promise<void> {
        if (this.isProcessing) {
            this.addMessage({
                type: 'error',
                content: 'Please wait for the current request to complete.',
                timestamp: new Date()
            });
            return;
        }

        this.isProcessing = true;
        
        this.addMessage({
            type: 'request',
            content: prompt,
            timestamp: new Date()
        });

        try {
            // Get relevant context from local codebase
            const context = await this.getRelevantContext(prompt);
            
            // Generate UI response (placeholder for now)
            const response = await this.generateUIResponse(prompt, context);
            
            this.addMessage({
                type: 'response',
                content: response,
                timestamp: new Date(),
                context: context
            });

        } catch (error) {
            this.addMessage({
                type: 'error',
                content: `Error generating UI: ${error}`,
                timestamp: new Date()
            });
        } finally {
            this.isProcessing = false;
        }
    }

    private async getRelevantContext(prompt: string): Promise<any> {
        // Use vector database to find relevant context
        const searchResults = await this.vectorDb.semanticSearch(prompt, 5);
        
        // Get current file context
        const activeEditor = vscode.window.activeTextEditor;
        const currentFileContext = activeEditor 
            ? this.vectorDb.getProjectContext(activeEditor.document.uri.fsPath)
            : null;

        // Get project analysis
        const projectAnalysis = await this.astAnalyzer.analyzeProject();

        return {
            searchResults,
            currentFile: currentFileContext,
            project: {
                framework: projectAnalysis.framework,
                stylingFramework: projectAnalysis.stylingFramework,
                hasAccessibility: projectAnalysis.accessibility,
                isResponsive: projectAnalysis.responsive
            }
        };
    }

    private async generateUIResponse(prompt: string, context: any): Promise<string> {
        // Placeholder for AI generation - will be connected to backend API
        const apiKey = vscode.workspace.getConfiguration('uiorbit').get<string>('apiKey');
        
        if (!apiKey) {
            return `
## ⚠️ API Key Required

To use UIOrbit, please set your API key in VS Code settings:

1. Open Settings (Ctrl+,)
2. Search for "UIOrbit API Key"
3. Enter your API key from [UIOrbit Dashboard](https://uiorbit.dev/dashboard)

---

**Context-Aware Analysis:**
- Framework: ${context.project.framework}
- Styling: ${context.project.stylingFramework}
- Accessibility: ${context.project.hasAccessibility ? '✅' : '❌'}
- Responsive: ${context.project.isResponsive ? '✅' : '❌'}
- Relevant patterns found: ${context.searchResults.length}

**Your request:** ${prompt}
            `;
        }

        // TODO: Implement actual API call to UIOrbit backend
        return `
## 🎨 Generated UI Component

\`\`\`tsx
interface ${this.extractComponentName(prompt)}Props {
  children?: React.ReactNode;
  className?: string;
}

export const ${this.extractComponentName(prompt)}: React.FC<${this.extractComponentName(prompt)}Props> = ({ 
  children, 
  className = '' 
}) => {
  return (
    <div className={\`modern-component \${className}\`}>
      {children}
    </div>
  );
};
\`\`\`

**Context Used:**
- Framework: ${context.project.framework}
- Styling: ${context.project.stylingFramework}
- Found ${context.searchResults.length} relevant patterns in your codebase

*This is a placeholder response. Connect your API key to get AI-generated components.*
        `;
    }

    private extractComponentName(prompt: string): string {
        // Simple extraction - can be enhanced
        const words = prompt.split(' ').filter(word => word.length > 2);
        return words.length > 0 ? words[0].charAt(0).toUpperCase() + words[0].slice(1) : 'Component';
    }

    private async sendProjectContext(): Promise<void> {
        const status = this.fileWatcher.getIndexingStatus();
        const trendingPatterns = this.vectorDb.getTrendingPatterns();
        
        this.webView?.webview.postMessage({
            type: 'project-context',
            data: {
                indexingStatus: status,
                trendingPatterns: trendingPatterns.slice(0, 5),
                totalPatterns: trendingPatterns.length
            }
        });
    }

    private addMessage(message: UIOrbitMessage): void {
        this.messages.push(message);
        this.updateWebview();
    }

    private clearConversation(): void {
        this.messages = [];
        this.updateWebview();
    }

    private updateWebview(): void {
        if (this.webView) {
            this.webView.webview.postMessage({
                type: 'update-messages',
                messages: this.messages
            });
        }
    }

    private getWebviewContent(): string {
        return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UIOrbit</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            font-size: var(--vscode-font-size);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            margin: 0;
            padding: 16px;
        }
        
        .header {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid var(--vscode-panel-border);
        }
        
        .logo {
            font-size: 18px;
            font-weight: bold;
            color: var(--vscode-textLink-foreground);
        }
        
        .status {
            margin-left: auto;
            font-size: 12px;
            color: var(--vscode-descriptionForeground);
        }
        
        .input-container {
            display: flex;
            gap: 8px;
            margin-bottom: 16px;
        }
        
        .input-field {
            flex: 1;
            padding: 8px 12px;
            background: var(--vscode-input-background);
            border: 1px solid var(--vscode-input-border);
            color: var(--vscode-input-foreground);
            border-radius: 4px;
        }
        
        .send-button {
            padding: 8px 16px;
            background: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .send-button:hover {
            background: var(--vscode-button-hoverBackground);
        }
        
        .send-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .messages {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .message {
            margin-bottom: 12px;
            padding: 8px;
            border-radius: 4px;
        }
        
        .message.request {
            background: var(--vscode-textBlockQuote-background);
            border-left: 3px solid var(--vscode-textLink-foreground);
        }
        
        .message.response {
            background: var(--vscode-editor-background);
            border: 1px solid var(--vscode-panel-border);
        }
        
        .message.error {
            background: var(--vscode-inputValidation-errorBackground);
            border-left: 3px solid var(--vscode-inputValidation-errorBorder);
        }
        
        .message.status {
            background: var(--vscode-inputValidation-infoBackground);
            border-left: 3px solid var(--vscode-inputValidation-infoBorder);
        }
        
        .timestamp {
            font-size: 11px;
            color: var(--vscode-descriptionForeground);
            margin-bottom: 4px;
        }
        
        .clear-button {
            padding: 4px 8px;
            background: var(--vscode-button-secondaryBackground);
            color: var(--vscode-button-secondaryForeground);
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
        
        pre {
            background: var(--vscode-textCodeBlock-background);
            padding: 8px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 13px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">🎨 UIOrbit</div>
        <div class="status" id="status">Ready</div>
    </div>
    
    <div class="input-container">
        <input 
            type="text" 
            class="input-field" 
            id="promptInput" 
            placeholder="Describe the UI component you want to generate..."
            onkeypress="handleKeyPress(event)"
        />
        <button class="send-button" id="sendButton" onclick="sendMessage()">Generate</button>
        <button class="clear-button" onclick="clearConversation()">Clear</button>
    </div>
    
    <div class="messages" id="messages"></div>
    
    <script>
        const vscode = acquireVsCodeApi();
        let isProcessing = false;
        
        function handleKeyPress(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }
        
        function sendMessage() {
            const input = document.getElementById('promptInput');
            const prompt = input.value.trim();
            
            if (!prompt || isProcessing) return;
            
            input.value = '';
            isProcessing = true;
            updateSendButton();
            
            vscode.postMessage({
                type: 'ui-request',
                content: prompt
            });
        }
        
        function clearConversation() {
            vscode.postMessage({ type: 'clear-conversation' });
        }
        
        function updateSendButton() {
            const button = document.getElementById('sendButton');
            button.disabled = isProcessing;
            button.textContent = isProcessing ? 'Generating...' : 'Generate';
        }
        
        function updateMessages(messages) {
            const container = document.getElementById('messages');
            container.innerHTML = '';
            
            messages.forEach(message => {
                const div = document.createElement('div');
                div.className = \`message \${message.type}\`;
                
                const timestamp = document.createElement('div');
                timestamp.className = 'timestamp';
                timestamp.textContent = new Date(message.timestamp).toLocaleTimeString();
                
                const content = document.createElement('div');
                content.innerHTML = message.content.replace(/\\n/g, '<br>');
                
                div.appendChild(timestamp);
                div.appendChild(content);
                container.appendChild(div);
            });
            
            container.scrollTop = container.scrollHeight;
            isProcessing = false;
            updateSendButton();
        }
        
        // Handle messages from extension
        window.addEventListener('message', event => {
            const message = event.data;
            
            switch (message.type) {
                case 'update-messages':
                    updateMessages(message.messages);
                    break;
                case 'project-context':
                    document.getElementById('status').textContent = 
                        \`\${message.data.totalPatterns} patterns indexed\`;
                    break;
            }
        });
        
        // Request initial context
        vscode.postMessage({ type: 'get-context' });
    </script>
</body>
</html>
        `;
    }

    public dispose(): void {
        this.fileWatcher.dispose();
    }
}
