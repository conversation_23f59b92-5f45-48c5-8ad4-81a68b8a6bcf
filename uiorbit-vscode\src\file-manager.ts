/**
 * UIOrbit Enhanced File Management System
 * Smart file creation, dependency management, and project structure optimization
 */

import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export interface FileStructure {
    path: string;
    content: string;
    type: 'component' | 'style' | 'test' | 'story' | 'config' | 'util';
}

export interface ProjectStructure {
    framework: string;
    structure: FileStructure[];
    dependencies: string[];
    devDependencies: string[];
}

export class EnhancedFileManager {
    private workspaceRoot: string;

    constructor(workspaceRoot: string) {
        this.workspaceRoot = workspaceRoot;
    }

    /**
     * Create optimized project structure based on framework
     */
    async createProjectStructure(framework: string, projectName: string): Promise<boolean> {
        try {
            const structure = this.getOptimalStructure(framework);
            const projectPath = path.join(this.workspaceRoot, projectName);

            // Create directory structure
            for (const file of structure.structure) {
                const fullPath = path.join(projectPath, file.path);
                const dir = path.dirname(fullPath);
                
                // Create directory if it doesn't exist
                if (!fs.existsSync(dir)) {
                    fs.mkdirSync(dir, { recursive: true });
                }

                // Create file with content
                fs.writeFileSync(fullPath, file.content);
            }

            // Install dependencies
            if (structure.dependencies.length > 0) {
                await this.installDependencies(projectPath, structure.dependencies, false);
            }

            if (structure.devDependencies.length > 0) {
                await this.installDependencies(projectPath, structure.devDependencies, true);
            }

            vscode.window.showInformationMessage(`✅ Project structure created for ${framework}`);
            return true;
        } catch (error) {
            vscode.window.showErrorMessage(`❌ Failed to create project structure: ${error}`);
            return false;
        }
    }

    /**
     * Smart component file creation with related files
     */
    async createComponentFiles(componentName: string, framework: string, options: {
        includeStyles?: boolean;
        includeTests?: boolean;
        includeStories?: boolean;
        includeIndex?: boolean;
    } = {}): Promise<string[]> {
        const files: string[] = [];
        const componentDir = path.join(this.workspaceRoot, 'src', 'components', componentName);

        // Create component directory
        if (!fs.existsSync(componentDir)) {
            fs.mkdirSync(componentDir, { recursive: true });
        }

        // Main component file
        const componentFile = this.createComponentFile(componentName, framework);
        const componentPath = path.join(componentDir, `${componentName}.${framework === 'vue' ? 'vue' : 'tsx'}`);
        fs.writeFileSync(componentPath, componentFile);
        files.push(componentPath);

        // Styles file
        if (options.includeStyles) {
            const stylesFile = this.createStylesFile(componentName);
            const stylesPath = path.join(componentDir, `${componentName}.module.css`);
            fs.writeFileSync(stylesPath, stylesFile);
            files.push(stylesPath);
        }

        // Test file
        if (options.includeTests) {
            const testFile = this.createTestFile(componentName, framework);
            const testPath = path.join(componentDir, `${componentName}.test.${framework === 'vue' ? 'js' : 'tsx'}`);
            fs.writeFileSync(testPath, testFile);
            files.push(testPath);
        }

        // Storybook file
        if (options.includeStories) {
            const storyFile = this.createStoryFile(componentName, framework);
            const storyPath = path.join(componentDir, `${componentName}.stories.${framework === 'vue' ? 'js' : 'tsx'}`);
            fs.writeFileSync(storyPath, storyFile);
            files.push(storyPath);
        }

        // Index file for clean imports
        if (options.includeIndex) {
            const indexFile = this.createIndexFile(componentName, framework);
            const indexPath = path.join(componentDir, 'index.ts');
            fs.writeFileSync(indexPath, indexFile);
            files.push(indexPath);
        }

        return files;
    }

    /**
     * Intelligent dependency management
     */
    async manageDependencies(requiredDeps: string[], framework: string): Promise<void> {
        const projectPath = this.getProjectPath();
        if (!projectPath) return;

        const packageJsonPath = path.join(projectPath, 'package.json');
        if (!fs.existsSync(packageJsonPath)) return;

        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
        const currentDeps = { ...packageJson.dependencies, ...packageJson.devDependencies };

        // Filter out already installed dependencies
        const missingDeps = requiredDeps.filter(dep => {
            const [name] = dep.split('@');
            return !currentDeps[name];
        });

        if (missingDeps.length > 0) {
            await this.installDependencies(projectPath, missingDeps, false);
        }

        // Add framework-specific optimizations
        await this.addFrameworkOptimizations(projectPath, framework);
    }

    /**
     * Auto-organize imports and exports
     */
    async organizeImports(filePath: string): Promise<void> {
        if (!fs.existsSync(filePath)) return;

        const content = fs.readFileSync(filePath, 'utf8');
        const organizedContent = this.optimizeImports(content);
        
        if (organizedContent !== content) {
            fs.writeFileSync(filePath, organizedContent);
        }
    }

    private getOptimalStructure(framework: string): ProjectStructure {
        const baseStructure = {
            framework,
            structure: [
                {
                    path: 'src/components/index.ts',
                    content: '// Export all components from this file\n',
                    type: 'config' as const
                },
                {
                    path: 'src/hooks/index.ts',
                    content: '// Export all custom hooks from this file\n',
                    type: 'util' as const
                },
                {
                    path: 'src/utils/index.ts',
                    content: '// Export all utility functions from this file\n',
                    type: 'util' as const
                },
                {
                    path: 'src/types/index.ts',
                    content: '// Export all TypeScript types from this file\n',
                    type: 'config' as const
                }
            ],
            dependencies: ['clsx', 'class-variance-authority'],
            devDependencies: ['@types/node', 'autoprefixer', 'postcss']
        };

        switch (framework) {
            case 'react':
                return {
                    ...baseStructure,
                    structure: [
                        ...baseStructure.structure,
                        {
                            path: 'src/components/ui/index.ts',
                            content: '// UI component exports\n',
                            type: 'config' as const
                        },
                        {
                            path: 'src/lib/utils.ts',
                            content: this.getUtilsContent(),
                            type: 'util' as const
                        }
                    ],
                    dependencies: [...baseStructure.dependencies, 'react', 'react-dom'],
                    devDependencies: [...baseStructure.devDependencies, '@types/react', '@types/react-dom']
                };

            case 'vue':
                return {
                    ...baseStructure,
                    structure: [
                        ...baseStructure.structure,
                        {
                            path: 'src/composables/index.ts',
                            content: '// Export all composables from this file\n',
                            type: 'util' as const
                        }
                    ],
                    dependencies: [...baseStructure.dependencies, 'vue'],
                    devDependencies: [...baseStructure.devDependencies, '@vitejs/plugin-vue']
                };

            default:
                return baseStructure;
        }
    }

    private createComponentFile(componentName: string, framework: string): string {
        switch (framework) {
            case 'react':
                return `import React from 'react';
import { cn } from '@/lib/utils';

interface ${componentName}Props {
  className?: string;
  children?: React.ReactNode;
}

export function ${componentName}({ className, children, ...props }: ${componentName}Props) {
  return (
    <div className={cn("", className)} {...props}>
      {children}
    </div>
  );
}

export default ${componentName};`;

            case 'vue':
                return `<template>
  <div class="${componentName.toLowerCase()}">
    <slot />
  </div>
</template>

<script setup lang="ts">
interface Props {
  // Define props here
}

const props = withDefaults(defineProps<Props>(), {
  // Default values
});
</script>

<style scoped>
.${componentName.toLowerCase()} {
  /* Component styles */
}
</style>`;

            default:
                return `// ${componentName} component\n`;
        }
    }

    private createStylesFile(componentName: string): string {
        return `.${componentName.toLowerCase()} {
  /* Base styles */
  display: block;
}

.${componentName.toLowerCase()}--variant {
  /* Variant styles */
}

.${componentName.toLowerCase()}--size-sm {
  /* Small size */
}

.${componentName.toLowerCase()}--size-md {
  /* Medium size */
}

.${componentName.toLowerCase()}--size-lg {
  /* Large size */
}`;
    }

    private createTestFile(componentName: string, framework: string): string {
        if (framework === 'react') {
            return `import { render, screen } from '@testing-library/react';
import { ${componentName} } from './${componentName}';

describe('${componentName}', () => {
  it('renders correctly', () => {
    render(<${componentName}>Test content</${componentName}>);
    expect(screen.getByText('Test content')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    render(<${componentName} className="custom-class">Test</${componentName}>);
    expect(screen.getByText('Test')).toHaveClass('custom-class');
  });
});`;
        }

        return `// Test file for ${componentName}\n`;
    }

    private createStoryFile(componentName: string, framework: string): string {
        if (framework === 'react') {
            return `import type { Meta, StoryObj } from '@storybook/react';
import { ${componentName} } from './${componentName}';

const meta: Meta<typeof ${componentName}> = {
  title: 'Components/${componentName}',
  component: ${componentName},
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    children: 'Default ${componentName}',
  },
};

export const WithCustomClass: Story = {
  args: {
    children: 'Custom ${componentName}',
    className: 'custom-styling',
  },
};`;
        }

        return `// Storybook story for ${componentName}\n`;
    }

    private createIndexFile(componentName: string, framework: string): string {
        return `export { ${componentName} } from './${componentName}';
export type { ${componentName}Props } from './${componentName}';`;
    }

    private getUtilsContent(): string {
        return `import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(date);
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}`;
    }

    private async installDependencies(projectPath: string, deps: string[], isDev: boolean): Promise<void> {
        const flag = isDev ? '--save-dev' : '--save';
        const command = `npm install ${flag} ${deps.join(' ')}`;
        await execAsync(command, { cwd: projectPath });
    }

    private async addFrameworkOptimizations(projectPath: string, framework: string): Promise<void> {
        // Add framework-specific configurations
        switch (framework) {
            case 'react':
                await this.addReactOptimizations(projectPath);
                break;
            case 'vue':
                await this.addVueOptimizations(projectPath);
                break;
        }
    }

    private async addReactOptimizations(projectPath: string): Promise<void> {
        // Add React-specific optimizations like ESLint rules, etc.
        const eslintConfig = {
            extends: ['@vitejs/eslint-config-react'],
            rules: {
                'react-hooks/exhaustive-deps': 'warn',
                'react/prop-types': 'off'
            }
        };

        const eslintPath = path.join(projectPath, '.eslintrc.json');
        if (!fs.existsSync(eslintPath)) {
            fs.writeFileSync(eslintPath, JSON.stringify(eslintConfig, null, 2));
        }
    }

    private async addVueOptimizations(projectPath: string): Promise<void> {
        // Add Vue-specific optimizations
    }

    private optimizeImports(content: string): string {
        // Simple import optimization - can be enhanced
        const lines = content.split('\n');
        const imports: string[] = [];
        const rest: string[] = [];

        lines.forEach(line => {
            if (line.trim().startsWith('import ')) {
                imports.push(line);
            } else {
                rest.push(line);
            }
        });

        // Sort imports
        imports.sort();

        return [...imports, '', ...rest].join('\n');
    }

    private getProjectPath(): string | null {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders || workspaceFolders.length === 0) return null;
        return workspaceFolders[0].uri.fsPath;
    }
}
