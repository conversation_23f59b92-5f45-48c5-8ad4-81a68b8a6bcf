#!/usr/bin/env node

/**
 * UIOrbit Launch Script
 * Helps with development, testing, and deployment tasks
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const commands = {
  dev: 'Start development environment',
  test: 'Run test suite',
  build: 'Build extension for production',
  package: 'Package extension for distribution',
  clean: 'Clean build artifacts',
  help: 'Show this help message'
};

function showHelp() {
  console.log('🚀 UIOrbit Development Helper\n');
  console.log('Usage: node scripts/launch.js <command>\n');
  console.log('Available commands:');
  Object.entries(commands).forEach(([cmd, desc]) => {
    console.log(`  ${cmd.padEnd(10)} - ${desc}`);
  });
  console.log('\nExamples:');
  console.log('  node scripts/launch.js dev     # Start development');
  console.log('  node scripts/launch.js test    # Run tests');
  console.log('  node scripts/launch.js build   # Build for production');
}

function runCommand(cmd) {
  console.log(`🔄 Running: ${cmd}`);
  try {
    execSync(cmd, { stdio: 'inherit', cwd: path.join(__dirname, '..') });
    console.log('✅ Command completed successfully');
  } catch (error) {
    console.error('❌ Command failed:', error.message);
    process.exit(1);
  }
}

function checkPrerequisites() {
  console.log('🔍 Checking prerequisites...');
  
  // Check Node.js version
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
  if (majorVersion < 16) {
    console.error('❌ Node.js 16+ required. Current version:', nodeVersion);
    process.exit(1);
  }
  console.log('✅ Node.js version:', nodeVersion);
  
  // Check if package.json exists
  const packagePath = path.join(__dirname, '..', 'package.json');
  if (!fs.existsSync(packagePath)) {
    console.error('❌ package.json not found');
    process.exit(1);
  }
  console.log('✅ package.json found');
  
  // Check if node_modules exists
  const nodeModulesPath = path.join(__dirname, '..', 'node_modules');
  if (!fs.existsSync(nodeModulesPath)) {
    console.log('📦 Installing dependencies...');
    runCommand('npm install');
  } else {
    console.log('✅ Dependencies installed');
  }
}

function dev() {
  console.log('🚀 Starting UIOrbit development environment...');
  checkPrerequisites();
  
  console.log('\n📝 Development Tips:');
  console.log('  - Press F5 in VS Code to launch Extension Development Host');
  console.log('  - Use Ctrl+Shift+P and search for "UIOrbit" commands');
  console.log('  - Check the Debug Console for logs and errors');
  console.log('  - Reload the Extension Development Host after code changes');
  
  runCommand('npm run watch');
}

function test() {
  console.log('🧪 Running UIOrbit test suite...');
  checkPrerequisites();
  runCommand('npm test');
}

function build() {
  console.log('🏗️ Building UIOrbit for production...');
  checkPrerequisites();
  runCommand('npm run compile');
  console.log('✅ Build completed successfully');
}

function packageExtension() {
  console.log('📦 Packaging UIOrbit extension...');
  checkPrerequisites();
  
  // Check if vsce is installed
  try {
    execSync('vsce --version', { stdio: 'pipe' });
  } catch (error) {
    console.log('📦 Installing vsce...');
    runCommand('npm install -g vsce');
  }
  
  // Build first
  build();
  
  // Package
  runCommand('vsce package');
  
  console.log('\n✅ Extension packaged successfully!');
  console.log('📁 Look for the .vsix file in the current directory');
  console.log('🚀 You can now install it with: code --install-extension uiorbit-vscode-*.vsix');
}

function clean() {
  console.log('🧹 Cleaning build artifacts...');
  
  const pathsToClean = [
    'out',
    '*.vsix',
    'node_modules/.cache',
    '.vscode-test'
  ];
  
  pathsToClean.forEach(p => {
    const fullPath = path.join(__dirname, '..', p);
    if (fs.existsSync(fullPath)) {
      console.log(`🗑️ Removing ${p}...`);
      if (p.includes('*')) {
        runCommand(`rm -f ${p}`);
      } else {
        runCommand(`rm -rf "${fullPath}"`);
      }
    }
  });
  
  console.log('✅ Cleanup completed');
}

function main() {
  const command = process.argv[2];
  
  if (!command || command === 'help') {
    showHelp();
    return;
  }
  
  switch (command) {
    case 'dev':
      dev();
      break;
    case 'test':
      test();
      break;
    case 'build':
      build();
      break;
    case 'package':
      packageExtension();
      break;
    case 'clean':
      clean();
      break;
    default:
      console.error(`❌ Unknown command: ${command}`);
      showHelp();
      process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  dev,
  test,
  build,
  packageExtension,
  clean
};
