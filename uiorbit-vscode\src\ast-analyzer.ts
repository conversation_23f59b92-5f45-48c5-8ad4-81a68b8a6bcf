/**
 * UIOrbit - AST-based code analysis for better context understanding
 * This module analyzes code structure to provide intelligent UI suggestions
 */

import * as vscode from 'vscode';
import * as path from 'path';

export interface ComponentInfo {
    name: string;
    type: 'functional' | 'class' | 'hook' | 'utility';
    props?: string[];
    imports: string[];
    exports: string[];
    dependencies: string[];
    framework: string;
    hasStyles: boolean;
    isResponsive: boolean;
    hasAccessibility: boolean;
    complexity: 'low' | 'medium' | 'high';
}

export interface ProjectAnalysis {
    framework: string;
    stylingFramework: string;
    components: ComponentInfo[];
    dependencies: string[];
    fileStructure: string[];
    designPatterns: string[];
    accessibility: boolean;
    responsive: boolean;
}

export class ASTAnalyzer {
    private workspaceRoot: string;

    constructor() {
        this.workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '';
    }

    public async analyzeProject(): Promise<ProjectAnalysis> {
        const packageJson = await this.readPackageJson();
        const framework = this.detectFramework(packageJson);
        const stylingFramework = this.detectStylingFramework(packageJson);
        
        const components = await this.analyzeComponents();
        const dependencies = Object.keys(packageJson?.dependencies || {});
        const fileStructure = await this.getFileStructure();
        const designPatterns = this.detectDesignPatterns(components);
        
        return {
            framework,
            stylingFramework,
            components,
            dependencies,
            fileStructure,
            designPatterns,
            accessibility: this.hasAccessibilityFeatures(components, dependencies),
            responsive: this.hasResponsiveDesign(components, dependencies)
        };
    }

    public async analyzeFile(filePath: string): Promise<ComponentInfo | null> {
        try {
            const document = await vscode.workspace.openTextDocument(filePath);
            const content = document.getText();
            const language = document.languageId;

            if (!this.isUIFile(language, content)) {
                return null;
            }

            return this.parseComponent(content, language, path.basename(filePath));
        } catch (error) {
            console.error('Error analyzing file:', error);
            return null;
        }
    }

    private async readPackageJson(): Promise<any> {
        try {
            const packagePath = path.join(this.workspaceRoot, 'package.json');
            const document = await vscode.workspace.openTextDocument(packagePath);
            return JSON.parse(document.getText());
        } catch {
            return {};
        }
    }

    private detectFramework(packageJson: any): string {
        const deps = { ...packageJson.dependencies, ...packageJson.devDependencies };
        
        if (deps.react) return 'React';
        if (deps.vue) return 'Vue';
        if (deps['@angular/core']) return 'Angular';
        if (deps.svelte) return 'Svelte';
        if (deps.next) return 'Next.js';
        if (deps.nuxt) return 'Nuxt.js';
        
        return 'HTML/CSS';
    }

    private detectStylingFramework(packageJson: any): string {
        const deps = { ...packageJson.dependencies, ...packageJson.devDependencies };
        
        if (deps.tailwindcss || deps['@tailwindcss/forms']) return 'Tailwind CSS';
        if (deps['styled-components']) return 'Styled Components';
        if (deps['@emotion/react'] || deps['@emotion/styled']) return 'Emotion';
        if (deps.sass || deps.scss) return 'SCSS/SASS';
        if (deps['css-modules']) return 'CSS Modules';
        if (deps['@mui/material']) return 'Material-UI';
        if (deps.antd) return 'Ant Design';
        if (deps['@chakra-ui/react']) return 'Chakra UI';
        
        return 'CSS';
    }

    private async analyzeComponents(): Promise<ComponentInfo[]> {
        const components: ComponentInfo[] = [];
        const files = await vscode.workspace.findFiles('**/*.{tsx,jsx,ts,js,vue,svelte}', '**/node_modules/**');
        
        for (const file of files.slice(0, 50)) { // Limit to avoid performance issues
            const component = await this.analyzeFile(file.fsPath);
            if (component) {
                components.push(component);
            }
        }
        
        return components;
    }

    private async getFileStructure(): Promise<string[]> {
        const files = await vscode.workspace.findFiles('**/*', '**/node_modules/**');
        return files.map(file => vscode.workspace.asRelativePath(file)).slice(0, 100);
    }

    private isUIFile(language: string, content: string): boolean {
        const uiLanguages = ['typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'vue', 'svelte'];
        if (!uiLanguages.includes(language)) return false;

        // Check for UI-related keywords
        const uiKeywords = [
            'component', 'jsx', 'tsx', 'render', 'return', 'div', 'span', 'button',
            'input', 'form', 'className', 'style', 'props', 'useState', 'useEffect'
        ];
        
        const contentLower = content.toLowerCase();
        return uiKeywords.some(keyword => contentLower.includes(keyword));
    }

    private parseComponent(content: string, language: string, fileName: string): ComponentInfo {
        const name = this.extractComponentName(content, fileName);
        const type = this.determineComponentType(content);
        const props = this.extractProps(content);
        const imports = this.extractImports(content);
        const exports = this.extractExports(content);
        const dependencies = this.extractDependencies(content);
        const framework = this.detectFrameworkFromContent(content);
        
        return {
            name,
            type,
            props,
            imports,
            exports,
            dependencies,
            framework,
            hasStyles: this.hasStyles(content),
            isResponsive: this.isResponsive(content),
            hasAccessibility: this.hasAccessibility(content),
            complexity: this.calculateComplexity(content)
        };
    }

    private extractComponentName(content: string, fileName: string): string {
        // Try to extract from function/class declarations
        const functionMatch = content.match(/(?:function|const|class)\s+([A-Z][a-zA-Z0-9]*)/);
        if (functionMatch) return functionMatch[1];
        
        // Fallback to filename
        return fileName.replace(/\.(tsx?|jsx?|vue|svelte)$/, '');
    }

    private determineComponentType(content: string): ComponentInfo['type'] {
        if (content.includes('class ') && content.includes('extends')) return 'class';
        if (content.includes('use') && content.match(/use[A-Z]/)) return 'hook';
        if (content.includes('function') || content.includes('const') || content.includes('=>')) return 'functional';
        return 'utility';
    }

    private extractProps(content: string): string[] {
        const props: string[] = [];
        
        // Extract from TypeScript interfaces
        const interfaceMatch = content.match(/interface\s+\w*Props\s*{([^}]*)}/s);
        if (interfaceMatch) {
            const propsContent = interfaceMatch[1];
            const propMatches = propsContent.match(/(\w+)(?:\?)?:/g);
            if (propMatches) {
                props.push(...propMatches.map(match => match.replace(/[?:]/g, '')));
            }
        }
        
        // Extract from destructuring
        const destructureMatch = content.match(/{\s*([^}]+)\s*}/);
        if (destructureMatch) {
            const destructured = destructureMatch[1].split(',').map(prop => prop.trim().split(':')[0].trim());
            props.push(...destructured);
        }
        
        return [...new Set(props)];
    }

    private extractImports(content: string): string[] {
        const imports: string[] = [];
        const importMatches = content.match(/import\s+.*?from\s+['"]([^'"]+)['"]/g);
        
        if (importMatches) {
            for (const match of importMatches) {
                const moduleMatch = match.match(/from\s+['"]([^'"]+)['"]/);
                if (moduleMatch) {
                    imports.push(moduleMatch[1]);
                }
            }
        }
        
        return imports;
    }

    private extractExports(content: string): string[] {
        const exports: string[] = [];
        const exportMatches = content.match(/export\s+(?:default\s+)?(?:function|const|class|interface|type)\s+(\w+)/g);
        
        if (exportMatches) {
            for (const match of exportMatches) {
                const nameMatch = match.match(/(?:function|const|class|interface|type)\s+(\w+)/);
                if (nameMatch) {
                    exports.push(nameMatch[1]);
                }
            }
        }
        
        return exports;
    }

    private extractDependencies(content: string): string[] {
        const deps: string[] = [];
        const imports = this.extractImports(content);
        
        for (const imp of imports) {
            if (!imp.startsWith('.') && !imp.startsWith('/')) {
                deps.push(imp.split('/')[0]);
            }
        }
        
        return [...new Set(deps)];
    }

    private detectFrameworkFromContent(content: string): string {
        if (content.includes('React') || content.includes('jsx') || content.includes('useState')) return 'React';
        if (content.includes('Vue') || content.includes('<template>')) return 'Vue';
        if (content.includes('@Component') || content.includes('Angular')) return 'Angular';
        if (content.includes('svelte') || content.includes('<script>')) return 'Svelte';
        return 'JavaScript';
    }

    private hasStyles(content: string): boolean {
        return content.includes('className') || 
               content.includes('style=') || 
               content.includes('styled') ||
               content.includes('css`') ||
               content.includes('.module.css');
    }

    private isResponsive(content: string): boolean {
        const responsiveKeywords = ['responsive', 'mobile', 'tablet', 'desktop', 'breakpoint', 'media', 'sm:', 'md:', 'lg:', 'xl:'];
        return responsiveKeywords.some(keyword => content.toLowerCase().includes(keyword));
    }

    private hasAccessibility(content: string): boolean {
        const a11yKeywords = ['aria-', 'role=', 'tabIndex', 'alt=', 'label', 'accessibility', 'screen reader'];
        return a11yKeywords.some(keyword => content.toLowerCase().includes(keyword));
    }

    private calculateComplexity(content: string): ComponentInfo['complexity'] {
        const lines = content.split('\n').length;
        const cyclomaticComplexity = (content.match(/if|else|for|while|switch|case|\?|&&|\|\|/g) || []).length;
        
        if (lines > 200 || cyclomaticComplexity > 15) return 'high';
        if (lines > 100 || cyclomaticComplexity > 8) return 'medium';
        return 'low';
    }

    private detectDesignPatterns(components: ComponentInfo[]): string[] {
        const patterns: string[] = [];
        
        // Check for common patterns
        if (components.some(c => c.name.includes('Provider'))) patterns.push('Provider Pattern');
        if (components.some(c => c.name.includes('Hook'))) patterns.push('Custom Hooks');
        if (components.some(c => c.name.includes('HOC'))) patterns.push('Higher-Order Components');
        if (components.some(c => c.name.includes('Container'))) patterns.push('Container/Presentational');
        
        return patterns;
    }

    private hasAccessibilityFeatures(components: ComponentInfo[], dependencies: string[]): boolean {
        return components.some(c => c.hasAccessibility) || 
               dependencies.some(dep => dep.includes('a11y') || dep.includes('accessibility'));
    }

    private hasResponsiveDesign(components: ComponentInfo[], dependencies: string[]): boolean {
        return components.some(c => c.isResponsive) || 
               dependencies.includes('tailwindcss') ||
               dependencies.includes('@media');
    }
}
