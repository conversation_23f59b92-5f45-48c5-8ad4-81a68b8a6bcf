/**
 * UIOrbit - File System Watcher for real-time context updates
 * Monitors project files and updates the vector database with changes
 */

import * as vscode from 'vscode';
import { VectorDatabase, ProjectContext } from './vector-database';
import { ASTAnalyzer } from './ast-analyzer';

export interface FileChangeEvent {
    type: 'created' | 'modified' | 'deleted';
    filePath: string;
    timestamp: Date;
}

export class FileWatcher {
    private watchers: vscode.FileSystemWatcher[] = [];
    private vectorDb: VectorDatabase;
    private astAnalyzer: ASTAnalyzer;
    private changeQueue: FileChangeEvent[] = [];
    private processingTimer: NodeJS.Timeout | null = null;

    constructor(
        private extensionContext: vscode.ExtensionContext,
        vectorDb: VectorDatabase
    ) {
        this.vectorDb = vectorDb;
        this.astAnalyzer = new ASTAnalyzer();
        this.initializeWatchers();
    }

    private initializeWatchers(): void {
        // Watch for UI-related file changes
        const patterns = [
            '**/*.{tsx,jsx,ts,js}',
            '**/*.vue',
            '**/*.svelte',
            '**/*.css',
            '**/*.scss',
            '**/*.sass',
            '**/*.less',
            '**/package.json',
            '**/tsconfig.json',
            '**/tailwind.config.js',
            '**/next.config.js',
            '**/vite.config.js'
        ];

        for (const pattern of patterns) {
            const watcher = vscode.workspace.createFileSystemWatcher(pattern);
            
            watcher.onDidCreate(uri => this.handleFileChange('created', uri.fsPath));
            watcher.onDidChange(uri => this.handleFileChange('modified', uri.fsPath));
            watcher.onDidDelete(uri => this.handleFileChange('deleted', uri.fsPath));
            
            this.watchers.push(watcher);
        }

        // Watch for active editor changes
        vscode.window.onDidChangeActiveTextEditor(editor => {
            if (editor && this.isUIFile(editor.document)) {
                this.handleActiveFileChange(editor.document);
            }
        });

        // Watch for document saves
        vscode.workspace.onDidSaveTextDocument(document => {
            if (this.isUIFile(document)) {
                this.handleFileChange('modified', document.uri.fsPath);
            }
        });
    }

    private handleFileChange(type: FileChangeEvent['type'], filePath: string): void {
        // Skip node_modules and other irrelevant directories
        if (this.shouldIgnoreFile(filePath)) {
            return;
        }

        const event: FileChangeEvent = {
            type,
            filePath,
            timestamp: new Date()
        };

        this.changeQueue.push(event);
        this.scheduleProcessing();
    }

    private scheduleProcessing(): void {
        // Debounce file changes to avoid excessive processing
        if (this.processingTimer) {
            clearTimeout(this.processingTimer);
        }

        this.processingTimer = setTimeout(() => {
            this.processChangeQueue();
        }, 1000); // Wait 1 second after last change
    }

    private async processChangeQueue(): Promise<void> {
        if (this.changeQueue.length === 0) return;

        const events = [...this.changeQueue];
        this.changeQueue = [];

        for (const event of events) {
            try {
                await this.processFileChange(event);
            } catch (error) {
                console.error(`Error processing file change for ${event.filePath}:`, error);
            }
        }
    }

    private async processFileChange(event: FileChangeEvent): Promise<void> {
        switch (event.type) {
            case 'created':
            case 'modified':
                await this.updateFileContext(event.filePath);
                break;
            case 'deleted':
                await this.removeFileContext(event.filePath);
                break;
        }
    }

    private async updateFileContext(filePath: string): Promise<void> {
        try {
            const componentInfo = await this.astAnalyzer.analyzeFile(filePath);
            if (!componentInfo) return;

            const document = await vscode.workspace.openTextDocument(filePath);
            const content = document.getText();
            
            const context: Omit<ProjectContext, 'id'> = {
                file_path: filePath,
                content: content.substring(0, 10000), // Limit content size
                language: document.languageId,
                framework: componentInfo.framework,
                dependencies: componentInfo.dependencies,
                imports: componentInfo.imports,
                exports: componentInfo.exports,
                components: [componentInfo.name],
                last_modified: new Date()
            };

            // Remove existing context for this file
            const existingContext = this.vectorDb.getProjectContext(filePath);
            if (existingContext) {
                // Update existing context
                Object.assign(existingContext, context);
            } else {
                // Add new context
                this.vectorDb.addProjectContext(context);
            }

        } catch (error) {
            console.error(`Error updating context for ${filePath}:`, error);
        }
    }

    private async removeFileContext(filePath: string): Promise<void> {
        // Note: VectorDatabase would need a removeProjectContext method
        // For now, we'll just log the deletion
        console.log(`File deleted: ${filePath}`);
    }

    private async handleActiveFileChange(document: vscode.TextDocument): Promise<void> {
        // Update context when user switches to a UI file
        if (this.isUIFile(document)) {
            await this.updateFileContext(document.uri.fsPath);
        }
    }

    private isUIFile(document: vscode.TextDocument): boolean {
        const uiExtensions = ['.tsx', '.jsx', '.ts', '.js', '.vue', '.svelte'];
        const uiLanguages = ['typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'vue', 'svelte'];
        
        return uiExtensions.some(ext => document.fileName.endsWith(ext)) ||
               uiLanguages.includes(document.languageId);
    }

    private shouldIgnoreFile(filePath: string): boolean {
        const ignorePaths = [
            'node_modules',
            '.git',
            'dist',
            'build',
            '.next',
            '.nuxt',
            'coverage',
            '.vscode',
            '.idea'
        ];

        return ignorePaths.some(ignore => filePath.includes(ignore));
    }

    public getRecentChanges(limit: number = 10): FileChangeEvent[] {
        return this.changeQueue
            .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
            .slice(0, limit);
    }

    public async getProjectSummary(): Promise<{
        totalFiles: number;
        recentChanges: number;
        frameworks: string[];
        components: number;
    }> {
        const projectAnalysis = await this.astAnalyzer.analyzeProject();
        const recentChanges = this.changeQueue.filter(
            event => event.timestamp > new Date(Date.now() - 24 * 60 * 60 * 1000)
        ).length;

        return {
            totalFiles: projectAnalysis.fileStructure.length,
            recentChanges,
            frameworks: [projectAnalysis.framework, projectAnalysis.stylingFramework],
            components: projectAnalysis.components.length
        };
    }

    public async getContextForCurrentFile(): Promise<{
        currentFile?: ProjectContext;
        relatedFiles: ProjectContext[];
        suggestions: string[];
    }> {
        const activeEditor = vscode.window.activeTextEditor;
        if (!activeEditor) {
            return { relatedFiles: [], suggestions: [] };
        }

        const currentFile = this.vectorDb.getProjectContext(activeEditor.document.uri.fsPath);
        const relatedFiles: ProjectContext[] = [];
        const suggestions: string[] = [];

        if (currentFile) {
            // Find related files based on imports/exports
            // This is a simplified implementation
            suggestions.push(
                'Consider adding responsive design',
                'Add accessibility features',
                'Apply modern styling patterns'
            );
        }

        return {
            currentFile,
            relatedFiles,
            suggestions
        };
    }

    public dispose(): void {
        // Clean up watchers
        for (const watcher of this.watchers) {
            watcher.dispose();
        }
        this.watchers = [];

        // Clear processing timer
        if (this.processingTimer) {
            clearTimeout(this.processingTimer);
            this.processingTimer = null;
        }
    }
}
