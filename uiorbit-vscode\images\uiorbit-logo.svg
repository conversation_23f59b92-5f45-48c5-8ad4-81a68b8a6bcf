<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle -->
  <circle cx="64" cy="64" r="60" fill="url(#gradient)" stroke="#2563eb" stroke-width="2"/>
  
  <!-- UI Elements representing components -->
  <rect x="20" y="30" width="88" height="8" rx="4" fill="#ffffff" opacity="0.9"/>
  <rect x="20" y="45" width="60" height="6" rx="3" fill="#ffffff" opacity="0.7"/>
  <rect x="20" y="56" width="40" height="6" rx="3" fill="#ffffff" opacity="0.7"/>
  
  <!-- Orbit rings -->
  <circle cx="64" cy="64" r="35" fill="none" stroke="#ffffff" stroke-width="1.5" opacity="0.4"/>
  <circle cx="64" cy="64" r="45" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.3"/>
  
  <!-- Central UI icon -->
  <rect x="52" y="75" width="24" height="18" rx="2" fill="#ffffff" stroke="#2563eb" stroke-width="1"/>
  <rect x="55" y="78" width="18" height="2" rx="1" fill="#2563eb"/>
  <rect x="55" y="82" width="12" height="2" rx="1" fill="#2563eb"/>
  <rect x="55" y="86" width="15" height="2" rx="1" fill="#2563eb"/>
  
  <!-- Orbiting elements -->
  <circle cx="94" cy="40" r="3" fill="#60a5fa"/>
  <circle cx="34" cy="88" r="3" fill="#60a5fa"/>
  <circle cx="90" cy="90" r="2.5" fill="#93c5fd"/>
  <circle cx="38" cy="38" r="2.5" fill="#93c5fd"/>
  
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
