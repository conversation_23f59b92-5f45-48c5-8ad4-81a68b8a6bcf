/**
 * UIOrbit Component Library Integration
 * Provides instant access to popular UI component libraries
 */

import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export interface ComponentLibraryConfig {
    name: 'shadcn' | 'mui' | 'antd' | 'chakra' | 'mantine' | 'headlessui';
    framework: 'react' | 'vue' | 'angular';
    components: string[];
}

export interface ComponentTemplate {
    name: string;
    description: string;
    code: string;
    dependencies: string[];
    category: 'form' | 'layout' | 'navigation' | 'feedback' | 'data' | 'media';
}

export class ComponentLibraryManager {
    private workspaceRoot: string;

    constructor(workspaceRoot: string) {
        this.workspaceRoot = workspaceRoot;
    }

    /**
     * Install a component library
     */
    async installLibrary(config: ComponentLibraryConfig): Promise<boolean> {
        try {
            const projectPath = this.getProjectPath();
            if (!projectPath) return false;

            vscode.window.showInformationMessage(`🚀 Installing ${config.name}...`);

            switch (config.name) {
                case 'shadcn':
                    await this.installShadcn(projectPath);
                    break;
                case 'mui':
                    await this.installMUI(projectPath);
                    break;
                case 'antd':
                    await this.installAntd(projectPath);
                    break;
                case 'chakra':
                    await this.installChakra(projectPath);
                    break;
                case 'mantine':
                    await this.installMantine(projectPath);
                    break;
                case 'headlessui':
                    await this.installHeadlessUI(projectPath);
                    break;
            }

            vscode.window.showInformationMessage(`✅ ${config.name} installed successfully!`);
            return true;
        } catch (error) {
            vscode.window.showErrorMessage(`❌ Failed to install ${config.name}: ${error}`);
            return false;
        }
    }

    /**
     * Get available component templates
     */
    getComponentTemplates(library: string): ComponentTemplate[] {
        switch (library) {
            case 'shadcn':
                return this.getShadcnTemplates();
            case 'mui':
                return this.getMUITemplates();
            case 'antd':
                return this.getAntdTemplates();
            case 'chakra':
                return this.getChakraTemplates();
            default:
                return [];
        }
    }

    /**
     * Generate component from template
     */
    async generateComponent(template: ComponentTemplate, componentName: string): Promise<string> {
        const code = template.code.replace(/{{componentName}}/g, componentName);
        
        // Install dependencies if needed
        if (template.dependencies.length > 0) {
            const projectPath = this.getProjectPath();
            if (projectPath) {
                const deps = template.dependencies.join(' ');
                await execAsync(`npm install ${deps}`, { cwd: projectPath });
            }
        }

        return code;
    }

    private getProjectPath(): string | null {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders || workspaceFolders.length === 0) return null;
        
        return workspaceFolders[0].uri.fsPath;
    }

    private async installShadcn(projectPath: string): Promise<void> {
        // Install shadcn/ui
        await execAsync('npx shadcn-ui@latest init -y', { cwd: projectPath });
        
        // Install common components
        const components = ['button', 'card', 'input', 'label', 'form', 'dialog', 'toast'];
        for (const component of components) {
            await execAsync(`npx shadcn-ui@latest add ${component} -y`, { cwd: projectPath });
        }
    }

    private async installMUI(projectPath: string): Promise<void> {
        await execAsync('npm install @mui/material @emotion/react @emotion/styled @mui/icons-material', { cwd: projectPath });
    }

    private async installAntd(projectPath: string): Promise<void> {
        await execAsync('npm install antd @ant-design/icons', { cwd: projectPath });
    }

    private async installChakra(projectPath: string): Promise<void> {
        await execAsync('npm install @chakra-ui/react @emotion/react @emotion/styled framer-motion', { cwd: projectPath });
    }

    private async installMantine(projectPath: string): Promise<void> {
        await execAsync('npm install @mantine/core @mantine/hooks @mantine/notifications @mantine/form', { cwd: projectPath });
    }

    private async installHeadlessUI(projectPath: string): Promise<void> {
        await execAsync('npm install @headlessui/react @heroicons/react', { cwd: projectPath });
    }

    private getShadcnTemplates(): ComponentTemplate[] {
        return [
            {
                name: 'Modern Button',
                description: 'A sleek, modern button with hover effects',
                category: 'form',
                dependencies: [],
                code: `import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"

interface {{componentName}}Props {
  children: React.ReactNode
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"
  size?: "default" | "sm" | "lg" | "icon"
  className?: string
  onClick?: () => void
}

export function {{componentName}}({ 
  children, 
  variant = "default", 
  size = "default", 
  className,
  onClick 
}: {{componentName}}Props) {
  return (
    <Button
      variant={variant}
      size={size}
      className={cn(
        "transition-all duration-200 hover:scale-105 active:scale-95",
        "shadow-lg hover:shadow-xl",
        "bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700",
        className
      )}
      onClick={onClick}
    >
      {children}
    </Button>
  )
}`
            },
            {
                name: 'Glassmorphism Card',
                description: 'A beautiful glassmorphism card component',
                category: 'layout',
                dependencies: [],
                code: `import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { cn } from "@/lib/utils"

interface {{componentName}}Props {
  title?: string
  description?: string
  children: React.ReactNode
  className?: string
}

export function {{componentName}}({ title, description, children, className }: {{componentName}}Props) {
  return (
    <Card className={cn(
      "backdrop-blur-md bg-white/10 border-white/20",
      "shadow-xl hover:shadow-2xl transition-all duration-300",
      "hover:bg-white/20 hover:border-white/30",
      "rounded-2xl overflow-hidden",
      className
    )}>
      {(title || description) && (
        <CardHeader className="pb-4">
          {title && (
            <CardTitle className="text-white font-bold text-xl">
              {title}
            </CardTitle>
          )}
          {description && (
            <CardDescription className="text-white/80">
              {description}
            </CardDescription>
          )}
        </CardHeader>
      )}
      <CardContent className="text-white">
        {children}
      </CardContent>
    </Card>
  )
}`
            },
            {
                name: 'Animated Form',
                description: 'A modern form with smooth animations',
                category: 'form',
                dependencies: ['react-hook-form', '@hookform/resolvers', 'zod'],
                code: `import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { cn } from "@/lib/utils"

const formSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(8, "Password must be at least 8 characters"),
})

interface {{componentName}}Props {
  onSubmit: (values: z.infer<typeof formSchema>) => void
  className?: string
}

export function {{componentName}}({ onSubmit, className }: {{componentName}}Props) {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  })

  return (
    <Form {...form}>
      <form 
        onSubmit={form.handleSubmit(onSubmit)} 
        className={cn("space-y-6 p-6", className)}
      >
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem className="space-y-2">
              <FormLabel className="text-sm font-medium">Email</FormLabel>
              <FormControl>
                <Input 
                  placeholder="Enter your email" 
                  {...field}
                  className="transition-all duration-200 focus:scale-105"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem className="space-y-2">
              <FormLabel className="text-sm font-medium">Password</FormLabel>
              <FormControl>
                <Input 
                  type="password" 
                  placeholder="Enter your password" 
                  {...field}
                  className="transition-all duration-200 focus:scale-105"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button 
          type="submit" 
          className="w-full transition-all duration-200 hover:scale-105"
        >
          Submit
        </Button>
      </form>
    </Form>
  )
}`
            }
        ];
    }

    private getMUITemplates(): ComponentTemplate[] {
        return [
            {
                name: 'Material Dashboard Card',
                description: 'A Material Design dashboard card',
                category: 'layout',
                dependencies: [],
                code: `import { Card, CardContent, Typography, Box, IconButton } from '@mui/material'
import { styled } from '@mui/material/styles'
import { MoreVert } from '@mui/icons-material'

const StyledCard = styled(Card)(({ theme }) => ({
  borderRadius: 16,
  boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 12px 48px rgba(0,0,0,0.15)',
  },
}))

interface {{componentName}}Props {
  title: string
  value: string | number
  subtitle?: string
  icon?: React.ReactNode
}

export function {{componentName}}({ title, value, subtitle, icon }: {{componentName}}Props) {
  return (
    <StyledCard>
      <CardContent sx={{ p: 3 }}>
        <Box display="flex" justifyContent="space-between" alignItems="flex-start">
          <Box>
            <Typography variant="h6" color="text.secondary" gutterBottom>
              {title}
            </Typography>
            <Typography variant="h3" component="div" fontWeight="bold">
              {value}
            </Typography>
            {subtitle && (
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                {subtitle}
              </Typography>
            )}
          </Box>
          <Box>
            {icon && (
              <Box sx={{ color: 'primary.main', fontSize: 40 }}>
                {icon}
              </Box>
            )}
            <IconButton size="small">
              <MoreVert />
            </IconButton>
          </Box>
        </Box>
      </CardContent>
    </StyledCard>
  )
}`
            }
        ];
    }

    private getAntdTemplates(): ComponentTemplate[] {
        return [
            {
                name: 'Ant Design Pro Table',
                description: 'A professional data table with search and filters',
                category: 'data',
                dependencies: [],
                code: `import { Table, Input, Button, Space, Tag } from 'antd'
import { SearchOutlined, FilterOutlined } from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'

interface DataType {
  key: string
  name: string
  status: string
  date: string
}

interface {{componentName}}Props {
  data: DataType[]
  loading?: boolean
}

export function {{componentName}}({ data, loading = false }: {{componentName}}Props) {
  const columns: ColumnsType<DataType> = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      sorter: (a, b) => a.name.localeCompare(b.name),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'active' ? 'green' : 'red'}>
          {status.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      sorter: (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime(),
    },
  ]

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <div className="mb-4 flex justify-between items-center">
        <h2 className="text-xl font-bold">Data Table</h2>
        <Space>
          <Input
            placeholder="Search..."
            prefix={<SearchOutlined />}
            style={{ width: 200 }}
          />
          <Button icon={<FilterOutlined />}>
            Filters
          </Button>
        </Space>
      </div>
      <Table
        columns={columns}
        dataSource={data}
        loading={loading}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
        }}
        className="shadow-sm"
      />
    </div>
  )
}`
            }
        ];
    }

    private getChakraTemplates(): ComponentTemplate[] {
        return [
            {
                name: 'Chakra Feature Card',
                description: 'A beautiful feature card with Chakra UI',
                category: 'layout',
                dependencies: [],
                code: `import {
  Box,
  Heading,
  Text,
  Stack,
  useColorModeValue,
  Icon,
} from '@chakra-ui/react'
import { motion } from 'framer-motion'

const MotionBox = motion(Box)

interface {{componentName}}Props {
  title: string
  description: string
  icon: React.ElementType
}

export function {{componentName}}({ title, description, icon }: {{componentName}}Props) {
  return (
    <MotionBox
      whileHover={{ y: -5 }}
      transition={{ duration: 0.2 }}
    >
      <Box
        maxW="sm"
        w="full"
        bg={useColorModeValue('white', 'gray.800')}
        boxShadow="2xl"
        rounded="lg"
        p={6}
        textAlign="center"
        border="1px"
        borderColor={useColorModeValue('gray.200', 'gray.700')}
      >
        <Icon
          as={icon}
          w={16}
          h={16}
          color="blue.500"
          mb={4}
        />
        <Heading fontSize="xl" fontFamily="body" mb={4}>
          {title}
        </Heading>
        <Text color={useColorModeValue('gray.600', 'gray.400')}>
          {description}
        </Text>
      </Box>
    </MotionBox>
  )
}`
            }
        ];
    }
}
