/**
 * UIOrbit Enterprise Features & API Monetization
 * Enterprise-grade features for business customers and API scaling
 */

import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export interface EnterpriseConfig {
    tier: 'free' | 'pro' | 'enterprise';
    apiKey: string;
    organizationId?: string;
    features: EnterpriseFeature[];
    limits: UsageLimits;
    billing: BillingInfo;
}

export interface EnterpriseFeature {
    name: string;
    enabled: boolean;
    description: string;
    tier: 'free' | 'pro' | 'enterprise';
}

export interface UsageLimits {
    monthlyRequests: number;
    usedRequests: number;
    teamMembers: number;
    projectsLimit: number;
    storageGB: number;
}

export interface BillingInfo {
    plan: string;
    nextBillingDate: Date;
    amount: number;
    currency: string;
}

export interface APIMetrics {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    averageResponseTime: number;
    popularEndpoints: { endpoint: string; count: number }[];
    revenue: number;
}

export interface TeamMember {
    id: string;
    name: string;
    email: string;
    role: 'admin' | 'developer' | 'viewer';
    joinedAt: Date;
    lastActive: Date;
}

export class EnterpriseManager {
    private config: EnterpriseConfig;
    private workspaceRoot: string;
    private apiMetrics: APIMetrics;

    constructor(workspaceRoot: string) {
        this.workspaceRoot = workspaceRoot;
        this.config = this.loadEnterpriseConfig();
        this.apiMetrics = this.initializeMetrics();
    }

    /**
     * Initialize enterprise features based on tier
     */
    async initializeEnterprise(): Promise<void> {
        const features = this.getAvailableFeatures();
        
        // Show enterprise dashboard
        this.showEnterpriseDashboard();
        
        // Setup usage tracking
        this.setupUsageTracking();
        
        // Initialize team features if enterprise
        if (this.config.tier === 'enterprise') {
            await this.initializeTeamFeatures();
        }
    }

    /**
     * Get available features based on tier
     */
    getAvailableFeatures(): EnterpriseFeature[] {
        const allFeatures: EnterpriseFeature[] = [
            {
                name: 'Basic Component Generation',
                enabled: true,
                description: 'Generate basic UI components',
                tier: 'free'
            },
            {
                name: 'Live Preview',
                enabled: this.config.tier !== 'free',
                description: 'Real-time component preview',
                tier: 'pro'
            },
            {
                name: 'Component Library Integration',
                enabled: this.config.tier !== 'free',
                description: 'Shadcn/UI, Material-UI, Ant Design integration',
                tier: 'pro'
            },
            {
                name: 'AI-Powered Code Analysis',
                enabled: this.config.tier !== 'free',
                description: 'Advanced AI code analysis and suggestions',
                tier: 'pro'
            },
            {
                name: 'Team Collaboration',
                enabled: this.config.tier === 'enterprise',
                description: 'Real-time team collaboration features',
                tier: 'enterprise'
            },
            {
                name: 'Custom Templates',
                enabled: this.config.tier === 'enterprise',
                description: 'Create and share custom component templates',
                tier: 'enterprise'
            },
            {
                name: 'API Access',
                enabled: this.config.tier === 'enterprise',
                description: 'Full API access for integrations',
                tier: 'enterprise'
            },
            {
                name: 'Priority Support',
                enabled: this.config.tier === 'enterprise',
                description: '24/7 priority customer support',
                tier: 'enterprise'
            }
        ];

        return allFeatures.filter(feature => feature.enabled);
    }

    /**
     * Check if feature is available for current tier
     */
    isFeatureAvailable(featureName: string): boolean {
        const feature = this.getAvailableFeatures().find(f => f.name === featureName);
        return feature ? feature.enabled : false;
    }

    /**
     * Track API usage for billing
     */
    trackAPIUsage(endpoint: string, success: boolean, responseTime: number): void {
        this.apiMetrics.totalRequests++;
        
        if (success) {
            this.apiMetrics.successfulRequests++;
        } else {
            this.apiMetrics.failedRequests++;
        }

        // Update average response time
        this.apiMetrics.averageResponseTime = 
            (this.apiMetrics.averageResponseTime + responseTime) / 2;

        // Track popular endpoints
        const endpointStat = this.apiMetrics.popularEndpoints.find(e => e.endpoint === endpoint);
        if (endpointStat) {
            endpointStat.count++;
        } else {
            this.apiMetrics.popularEndpoints.push({ endpoint, count: 1 });
        }

        // Update usage limits
        this.config.limits.usedRequests++;

        // Calculate revenue (example: $0.01 per request)
        if (success) {
            this.apiMetrics.revenue += 0.01;
        }

        // Check usage limits
        this.checkUsageLimits();
    }

    /**
     * Check if usage limits are exceeded
     */
    private checkUsageLimits(): void {
        const { monthlyRequests, usedRequests } = this.config.limits;
        const usagePercentage = (usedRequests / monthlyRequests) * 100;

        if (usagePercentage >= 90) {
            vscode.window.showWarningMessage(
                `⚠️ You've used ${usagePercentage.toFixed(1)}% of your monthly API requests. Consider upgrading your plan.`,
                'Upgrade Plan',
                'View Usage'
            ).then(action => {
                if (action === 'Upgrade Plan') {
                    this.showUpgradeOptions();
                } else if (action === 'View Usage') {
                    this.showUsageDashboard();
                }
            });
        }
    }

    /**
     * Show enterprise dashboard
     */
    showEnterpriseDashboard(): void {
        const panel = vscode.window.createWebviewPanel(
            'uiorbitEnterprise',
            '🏢 UIOrbit Enterprise Dashboard',
            vscode.ViewColumn.One,
            {
                enableScripts: true,
                retainContextWhenHidden: true
            }
        );

        panel.webview.html = this.getEnterpriseDashboardHtml();
        
        panel.webview.onDidReceiveMessage(message => {
            switch (message.command) {
                case 'upgrade':
                    this.showUpgradeOptions();
                    break;
                case 'manageTeam':
                    this.showTeamManagement();
                    break;
                case 'viewMetrics':
                    this.showAPIMetrics();
                    break;
                case 'exportData':
                    this.exportEnterpriseData();
                    break;
            }
        });
    }

    /**
     * Show upgrade options
     */
    private async showUpgradeOptions(): Promise<void> {
        const options = [
            'Pro Plan - $19/month',
            'Enterprise Plan - $99/month',
            'Contact Sales'
        ];

        const selected = await vscode.window.showQuickPick(options, {
            placeHolder: 'Choose your plan'
        });

        if (selected) {
            if (selected.includes('Pro')) {
                await this.upgradeToPro();
            } else if (selected.includes('Enterprise')) {
                await this.upgradeToEnterprise();
            } else {
                vscode.env.openExternal(vscode.Uri.parse('https://uiorbit.com/contact-sales'));
            }
        }
    }

    /**
     * Upgrade to Pro plan
     */
    private async upgradeToPro(): Promise<void> {
        // In a real implementation, this would integrate with a payment processor
        this.config.tier = 'pro';
        this.config.limits.monthlyRequests = 10000;
        this.config.billing = {
            plan: 'Pro',
            nextBillingDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
            amount: 19,
            currency: 'USD'
        };

        this.saveEnterpriseConfig();
        vscode.window.showInformationMessage('🎉 Upgraded to Pro plan! Enjoy enhanced features.');
    }

    /**
     * Upgrade to Enterprise plan
     */
    private async upgradeToEnterprise(): Promise<void> {
        this.config.tier = 'enterprise';
        this.config.limits.monthlyRequests = 100000;
        this.config.limits.teamMembers = 50;
        this.config.billing = {
            plan: 'Enterprise',
            nextBillingDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
            amount: 99,
            currency: 'USD'
        };

        this.saveEnterpriseConfig();
        vscode.window.showInformationMessage('🚀 Upgraded to Enterprise plan! Full features unlocked.');
    }

    /**
     * Show team management interface
     */
    private showTeamManagement(): void {
        if (this.config.tier !== 'enterprise') {
            vscode.window.showInformationMessage(
                'Team management is available in Enterprise plan only.',
                'Upgrade to Enterprise'
            ).then(action => {
                if (action === 'Upgrade to Enterprise') {
                    this.showUpgradeOptions();
                }
            });
            return;
        }

        // Show team management panel
        const panel = vscode.window.createWebviewPanel(
            'uiorbitTeam',
            '👥 Team Management',
            vscode.ViewColumn.One,
            { enableScripts: true }
        );

        panel.webview.html = this.getTeamManagementHtml();
    }

    /**
     * Show API metrics dashboard
     */
    private showAPIMetrics(): void {
        const panel = vscode.window.createWebviewPanel(
            'uiorbitMetrics',
            '📊 API Metrics',
            vscode.ViewColumn.One,
            { enableScripts: true }
        );

        panel.webview.html = this.getAPIMetricsHtml();
    }

    /**
     * Export enterprise data
     */
    private async exportEnterpriseData(): Promise<void> {
        const data = {
            config: this.config,
            metrics: this.apiMetrics,
            exportDate: new Date().toISOString()
        };

        const exportPath = path.join(this.workspaceRoot, 'uiorbit-enterprise-export.json');
        fs.writeFileSync(exportPath, JSON.stringify(data, null, 2));

        vscode.window.showInformationMessage(
            `📁 Enterprise data exported to: ${exportPath}`,
            'Open File'
        ).then(action => {
            if (action === 'Open File') {
                vscode.workspace.openTextDocument(exportPath).then(doc => {
                    vscode.window.showTextDocument(doc);
                });
            }
        });
    }

    private loadEnterpriseConfig(): EnterpriseConfig {
        const configPath = path.join(this.workspaceRoot, '.uiorbit', 'enterprise.json');
        
        if (fs.existsSync(configPath)) {
            try {
                return JSON.parse(fs.readFileSync(configPath, 'utf8'));
            } catch (error) {
                console.error('Failed to load enterprise config:', error);
            }
        }

        // Default free tier config
        return {
            tier: 'free',
            apiKey: '',
            features: [],
            limits: {
                monthlyRequests: 1000,
                usedRequests: 0,
                teamMembers: 1,
                projectsLimit: 3,
                storageGB: 1
            },
            billing: {
                plan: 'Free',
                nextBillingDate: new Date(),
                amount: 0,
                currency: 'USD'
            }
        };
    }

    private saveEnterpriseConfig(): void {
        const configDir = path.join(this.workspaceRoot, '.uiorbit');
        const configPath = path.join(configDir, 'enterprise.json');

        if (!fs.existsSync(configDir)) {
            fs.mkdirSync(configDir, { recursive: true });
        }

        fs.writeFileSync(configPath, JSON.stringify(this.config, null, 2));
    }

    private initializeMetrics(): APIMetrics {
        return {
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            averageResponseTime: 0,
            popularEndpoints: [],
            revenue: 0
        };
    }

    private setupUsageTracking(): void {
        // Setup periodic usage reporting
        setInterval(() => {
            this.reportUsageMetrics();
        }, 60000); // Report every minute
    }

    private reportUsageMetrics(): void {
        // In a real implementation, this would send metrics to analytics service
        console.log('Usage metrics:', this.apiMetrics);
    }

    private async initializeTeamFeatures(): Promise<void> {
        // Initialize team-specific features
        vscode.window.showInformationMessage('🚀 Enterprise team features initialized!');
    }

    private showUsageDashboard(): void {
        const panel = vscode.window.createWebviewPanel(
            'uiorbitUsage',
            '📈 Usage Dashboard',
            vscode.ViewColumn.One,
            { enableScripts: true }
        );

        panel.webview.html = this.getUsageDashboardHtml();
    }

    private getEnterpriseDashboardHtml(): string {
        return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>UIOrbit Enterprise Dashboard</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0;
        }
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .metric {
            font-size: 2em;
            font-weight: bold;
            color: #4ecdc4;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin: 5px;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }
        .tier-badge {
            background: #4ecdc4;
            color: #333;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>🏢 UIOrbit Enterprise Dashboard</h1>
    <div class="tier-badge">${this.config.tier.toUpperCase()} PLAN</div>
    
    <div class="dashboard">
        <div class="card">
            <h3>📊 Usage Statistics</h3>
            <div class="metric">${this.config.limits.usedRequests}</div>
            <p>API Requests Used</p>
            <p>Limit: ${this.config.limits.monthlyRequests}/month</p>
        </div>
        
        <div class="card">
            <h3>💰 Revenue</h3>
            <div class="metric">$${this.apiMetrics.revenue.toFixed(2)}</div>
            <p>Total Revenue</p>
        </div>
        
        <div class="card">
            <h3>👥 Team</h3>
            <div class="metric">${this.config.limits.teamMembers}</div>
            <p>Team Members</p>
            <button onclick="manageTeam()">Manage Team</button>
        </div>
        
        <div class="card">
            <h3>⚡ Performance</h3>
            <div class="metric">${this.apiMetrics.averageResponseTime.toFixed(0)}ms</div>
            <p>Avg Response Time</p>
            <button onclick="viewMetrics()">View Metrics</button>
        </div>
    </div>
    
    <div class="card" style="margin-top: 20px;">
        <h3>🚀 Quick Actions</h3>
        <button onclick="upgrade()">Upgrade Plan</button>
        <button onclick="exportData()">Export Data</button>
        <button onclick="viewMetrics()">View Analytics</button>
    </div>

    <script>
        const vscode = acquireVsCodeApi();
        
        function upgrade() {
            vscode.postMessage({ command: 'upgrade' });
        }
        
        function manageTeam() {
            vscode.postMessage({ command: 'manageTeam' });
        }
        
        function viewMetrics() {
            vscode.postMessage({ command: 'viewMetrics' });
        }
        
        function exportData() {
            vscode.postMessage({ command: 'exportData' });
        }
    </script>
</body>
</html>`;
    }

    private getTeamManagementHtml(): string {
        return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Team Management</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .team-member {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        button {
            background: #4ecdc4;
            color: #333;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <h1>👥 Team Management</h1>
    <p>Manage your UIOrbit team members and permissions.</p>
    
    <div class="team-member">
        <div>
            <strong>You (Admin)</strong><br>
            <small><EMAIL></small>
        </div>
        <button>Manage</button>
    </div>
    
    <button onclick="inviteTeamMember()" style="margin-top: 20px;">+ Invite Team Member</button>
    
    <script>
        function inviteTeamMember() {
            alert('Team invitation feature coming soon!');
        }
    </script>
</body>
</html>`;
    }

    private getAPIMetricsHtml(): string {
        return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>API Metrics</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .metric-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 12px;
            text-align: center;
        }
        .metric-value {
            font-size: 2.5em;
            font-weight: bold;
            color: #4ecdc4;
        }
    </style>
</head>
<body>
    <h1>📊 API Metrics Dashboard</h1>
    
    <div class="metrics-grid">
        <div class="metric-card">
            <div class="metric-value">${this.apiMetrics.totalRequests}</div>
            <p>Total Requests</p>
        </div>
        <div class="metric-card">
            <div class="metric-value">${this.apiMetrics.successfulRequests}</div>
            <p>Successful Requests</p>
        </div>
        <div class="metric-card">
            <div class="metric-value">${this.apiMetrics.failedRequests}</div>
            <p>Failed Requests</p>
        </div>
        <div class="metric-card">
            <div class="metric-value">$${this.apiMetrics.revenue.toFixed(2)}</div>
            <p>Revenue Generated</p>
        </div>
    </div>
    
    <h2>📈 Revenue Projection</h2>
    <p>Based on current usage patterns:</p>
    <ul>
        <li><strong>Year 1:</strong> $${(this.apiMetrics.revenue * 12 * 10).toFixed(0)} (projected)</li>
        <li><strong>Year 2:</strong> $${(this.apiMetrics.revenue * 12 * 35).toFixed(0)} (projected)</li>
    </ul>
</body>
</html>`;
    }

    private getUsageDashboardHtml(): string {
        const usagePercentage = (this.config.limits.usedRequests / this.config.limits.monthlyRequests) * 100;
        
        return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Usage Dashboard</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .usage-bar {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            height: 20px;
            margin: 10px 0;
            overflow: hidden;
        }
        .usage-fill {
            background: #4ecdc4;
            height: 100%;
            width: ${usagePercentage}%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <h1>📈 Usage Dashboard</h1>
    
    <h3>API Requests</h3>
    <div class="usage-bar">
        <div class="usage-fill"></div>
    </div>
    <p>${this.config.limits.usedRequests} / ${this.config.limits.monthlyRequests} requests used (${usagePercentage.toFixed(1)}%)</p>
    
    <h3>Current Plan: ${this.config.billing.plan}</h3>
    <p>Next billing: ${this.config.billing.nextBillingDate.toLocaleDateString()}</p>
    <p>Amount: $${this.config.billing.amount}/${this.config.billing.currency}</p>
</body>
</html>`;
    }
}
