"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/extension.ts
var extension_exports = {};
__export(extension_exports, {
  activate: () => activate,
  deactivate: () => deactivate
});
module.exports = __toCommonJS(extension_exports);
var vscode4 = __toESM(require("vscode"));

// src/clean-view-provider.ts
var vscode3 = __toESM(require("vscode"));

// src/vector-database.ts
var fs = __toESM(require("fs"));
var path = __toESM(require("path"));
var VectorDatabase = class {
  constructor(extensionContext) {
    this.extensionContext = extensionContext;
    this.patterns = /* @__PURE__ */ new Map();
    this.contexts = /* @__PURE__ */ new Map();
    this.isInitialized = false;
    this.dbPath = path.join(extensionContext.globalStorageUri.fsPath, "uiorbit.db");
    this.initializeDatabase();
  }
  async initializeDatabase() {
    try {
      await this.loadDatabase();
      this.initializeDefaultPatterns();
      this.isInitialized = true;
    } catch (error) {
      console.error("Failed to initialize UIOrbit database:", error);
      this.isInitialized = true;
    }
  }
  async loadDatabase() {
    try {
      if (fs.existsSync(this.dbPath)) {
        const data = JSON.parse(fs.readFileSync(this.dbPath, "utf8"));
        if (data.patterns) {
          for (const pattern of data.patterns) {
            this.patterns.set(pattern.id, {
              ...pattern,
              created_at: new Date(pattern.created_at),
              updated_at: new Date(pattern.updated_at)
            });
          }
        }
        if (data.contexts) {
          for (const context of data.contexts) {
            this.contexts.set(context.id, {
              ...context,
              last_modified: new Date(context.last_modified)
            });
          }
        }
      }
    } catch (error) {
      console.error("Failed to load UIOrbit database:", error);
    }
  }
  async saveDatabase() {
    try {
      const dir = path.dirname(this.dbPath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
      const data = {
        patterns: Array.from(this.patterns.values()),
        contexts: Array.from(this.contexts.values()),
        version: "1.0.0",
        last_updated: new Date().toISOString()
      };
      fs.writeFileSync(this.dbPath, JSON.stringify(data, null, 2));
    } catch (error) {
      console.error("Failed to save UIOrbit database:", error);
    }
  }
  initializeDefaultPatterns() {
    const defaultPatterns = [
      {
        name: "React Button Component",
        description: "Modern, accessible button component with variants",
        framework: "React",
        category: "component",
        tags: ["button", "interactive", "accessible"],
        code: `interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  children: React.ReactNode;
  onClick?: () => void;
}

export const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  disabled = false,
  children,
  onClick
}) => {
  const baseClasses = 'font-medium rounded-lg transition-colors focus:outline-none focus:ring-2';
  const variantClasses = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',
    secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',
    outline: 'border-2 border-blue-600 text-blue-600 hover:bg-blue-50 focus:ring-blue-500'
  };
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg'
  };

  return (
    <button
      className={\`\${baseClasses} \${variantClasses[variant]} \${sizeClasses[size]} \${disabled ? 'opacity-50 cursor-not-allowed' : ''}\`}
      disabled={disabled}
      onClick={onClick}
      aria-disabled={disabled}
    >
      {children}
    </button>
  );
};`,
        usageCount: 0,
        embedding: new Float32Array(384),
        trendScore: 0.8,
        accessibilityScore: 0.9,
        performanceScore: 0.85
      },
      {
        name: "Responsive Card Layout",
        description: "Flexible card component with responsive design",
        framework: "React",
        category: "layout",
        tags: ["card", "responsive", "layout"],
        code: `interface CardProps {
  title?: string;
  children: React.ReactNode;
  className?: string;
}

export const Card: React.FC<CardProps> = ({ title, children, className = '' }) => {
  return (
    <div className={\`bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden \${className}\`}>
      {title && (
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
        </div>
      )}
      <div className="p-6">
        {children}
      </div>
    </div>
  );
};`,
        usageCount: 0,
        embedding: new Float32Array(384),
        trendScore: 0.7,
        accessibilityScore: 0.8,
        performanceScore: 0.9
      }
    ];
    for (const pattern of defaultPatterns) {
      const id = this.generateId();
      const now = new Date();
      this.patterns.set(id, {
        ...pattern,
        id,
        createdAt: now,
        updatedAt: now
      });
    }
    this.saveDatabase();
  }
  addPattern(pattern) {
    const id = this.generateId();
    const now = new Date();
    const newPattern = {
      ...pattern,
      id,
      createdAt: now,
      updatedAt: now
    };
    this.patterns.set(id, newPattern);
    this.saveDatabase();
    return id;
  }
  getPattern(id) {
    return this.patterns.get(id);
  }
  searchPatterns(query, framework, category) {
    const results = [];
    const queryLower = query.toLowerCase();
    for (const pattern of this.patterns.values()) {
      if (framework && pattern.framework !== framework) {
        continue;
      }
      if (category && pattern.category !== category) {
        continue;
      }
      const searchText = `${pattern.name} ${pattern.description} ${pattern.tags.join(" ")}`.toLowerCase();
      if (searchText.includes(queryLower)) {
        results.push(pattern);
      }
    }
    return results.sort((a, b) => b.usageCount - a.usageCount);
  }
  addProjectContext(context) {
    const id = this.generateId();
    const newContext = {
      ...context,
      id
    };
    this.contexts.set(id, newContext);
    this.saveDatabase();
    return id;
  }
  getProjectContext(filePath) {
    for (const context of this.contexts.values()) {
      if (context.filePath === filePath) {
        return context;
      }
    }
    return void 0;
  }
  updatePatternUsage(id) {
    const pattern = this.patterns.get(id);
    if (pattern) {
      pattern.usageCount++;
      pattern.updatedAt = new Date();
      this.saveDatabase();
    }
  }
  getPopularPatterns(limit = 10) {
    return Array.from(this.patterns.values()).sort((a, b) => b.usageCount - a.usageCount).slice(0, limit);
  }
  getPatternsByFramework(framework) {
    return Array.from(this.patterns.values()).filter((pattern) => pattern.framework === framework);
  }
  generateId() {
    return Math.random().toString(36).substring(2, 11);
  }
  cleanup() {
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1e3);
    for (const [id, context] of this.contexts.entries()) {
      if (context.lastModified < thirtyDaysAgo) {
        this.contexts.delete(id);
      }
    }
    this.saveDatabase();
  }
  async semanticSearch(query, limit = 10) {
    const results = [];
    for (const pattern of this.patterns.values()) {
      const searchText = `${pattern.name} ${pattern.description} ${pattern.tags.join(" ")} ${pattern.code}`.toLowerCase();
      if (searchText.includes(query.toLowerCase())) {
        results.push({
          item: pattern,
          similarity: this.calculateTextSimilarity(query, searchText),
          type: "pattern"
        });
      }
    }
    for (const context of this.contexts.values()) {
      const searchText = `${context.filePath} ${context.content} ${context.components.join(" ")}`.toLowerCase();
      if (searchText.includes(query.toLowerCase())) {
        results.push({
          item: context,
          similarity: this.calculateTextSimilarity(query, searchText),
          type: "context"
        });
      }
    }
    return results.sort((a, b) => b.similarity - a.similarity).slice(0, limit);
  }
  getRelevantContext(filePath, _query) {
    const contexts = [];
    const currentDir = path.dirname(filePath);
    for (const context of this.contexts.values()) {
      const contextDir = path.dirname(context.filePath);
      if (contextDir === currentDir) {
        contexts.push(context);
      } else if (this.areFilesRelated(filePath, context.filePath)) {
        contexts.push(context);
      }
    }
    return contexts.slice(0, 5);
  }
  calculateTextSimilarity(query, text) {
    const queryWords = query.toLowerCase().split(/\s+/);
    const textWords = text.toLowerCase().split(/\s+/);
    let matches = 0;
    for (const word of queryWords) {
      if (textWords.some((textWord) => textWord.includes(word))) {
        matches++;
      }
    }
    return matches / queryWords.length;
  }
  areFilesRelated(file1, file2) {
    const context1 = this.getProjectContext(file1);
    const context2 = this.getProjectContext(file2);
    if (!context1 || !context2) {
      return false;
    }
    const sharedDeps = context1.dependencies.filter(
      (dep) => context2.dependencies.includes(dep)
    );
    return sharedDeps.length > 0;
  }
  getTrendingPatterns() {
    return Array.from(this.patterns.values()).sort((a, b) => b.trendScore - a.trendScore).slice(0, 10);
  }
  getAccessiblePatterns() {
    return Array.from(this.patterns.values()).filter((pattern) => pattern.accessibilityScore > 0.7).sort((a, b) => b.accessibilityScore - a.accessibilityScore);
  }
  getPerformantPatterns() {
    return Array.from(this.patterns.values()).filter((pattern) => pattern.performanceScore > 0.8).sort((a, b) => b.performanceScore - a.performanceScore);
  }
};

// src/file-watcher.ts
var vscode2 = __toESM(require("vscode"));

// src/ast-analyzer.ts
var vscode = __toESM(require("vscode"));
var path2 = __toESM(require("path"));
var ASTAnalyzer = class {
  constructor() {
    this.workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || "";
  }
  async analyzeProject() {
    const packageJson = await this.readPackageJson();
    const framework = this.detectFramework(packageJson);
    const stylingFramework = this.detectStylingFramework(packageJson);
    const components = await this.analyzeComponents();
    const dependencies = Object.keys(packageJson?.dependencies || {});
    const fileStructure = await this.getFileStructure();
    const designPatterns = this.detectDesignPatterns(components);
    return {
      framework,
      stylingFramework,
      components,
      dependencies,
      fileStructure,
      designPatterns,
      accessibility: this.hasAccessibilityFeatures(components, dependencies),
      responsive: this.hasResponsiveDesign(components, dependencies)
    };
  }
  async analyzeFile(filePath) {
    try {
      const document = await vscode.workspace.openTextDocument(filePath);
      const content = document.getText();
      const language = document.languageId;
      if (!this.isUIFile(language, content)) {
        return null;
      }
      return this.parseComponent(content, language, path2.basename(filePath));
    } catch (error) {
      console.error("Error analyzing file:", error);
      return null;
    }
  }
  async readPackageJson() {
    try {
      const packagePath = path2.join(this.workspaceRoot, "package.json");
      const document = await vscode.workspace.openTextDocument(packagePath);
      return JSON.parse(document.getText());
    } catch {
      return {};
    }
  }
  detectFramework(packageJson) {
    const deps = { ...packageJson.dependencies, ...packageJson.devDependencies };
    if (deps.react)
      return "React";
    if (deps.vue)
      return "Vue";
    if (deps["@angular/core"])
      return "Angular";
    if (deps.svelte)
      return "Svelte";
    if (deps.next)
      return "Next.js";
    if (deps.nuxt)
      return "Nuxt.js";
    return "HTML/CSS";
  }
  detectStylingFramework(packageJson) {
    const deps = { ...packageJson.dependencies, ...packageJson.devDependencies };
    if (deps.tailwindcss || deps["@tailwindcss/forms"])
      return "Tailwind CSS";
    if (deps["styled-components"])
      return "Styled Components";
    if (deps["@emotion/react"] || deps["@emotion/styled"])
      return "Emotion";
    if (deps.sass || deps.scss)
      return "SCSS/SASS";
    if (deps["css-modules"])
      return "CSS Modules";
    if (deps["@mui/material"])
      return "Material-UI";
    if (deps.antd)
      return "Ant Design";
    if (deps["@chakra-ui/react"])
      return "Chakra UI";
    return "CSS";
  }
  async analyzeComponents() {
    const components = [];
    const files = await vscode.workspace.findFiles("**/*.{tsx,jsx,ts,js,vue,svelte}", "**/node_modules/**");
    for (const file of files.slice(0, 50)) {
      const component = await this.analyzeFile(file.fsPath);
      if (component) {
        components.push(component);
      }
    }
    return components;
  }
  async getFileStructure() {
    const files = await vscode.workspace.findFiles("**/*", "**/node_modules/**");
    return files.map((file) => vscode.workspace.asRelativePath(file)).slice(0, 100);
  }
  isUIFile(language, content) {
    const uiLanguages = ["typescript", "javascript", "typescriptreact", "javascriptreact", "vue", "svelte"];
    if (!uiLanguages.includes(language))
      return false;
    const uiKeywords = [
      "component",
      "jsx",
      "tsx",
      "render",
      "return",
      "div",
      "span",
      "button",
      "input",
      "form",
      "className",
      "style",
      "props",
      "useState",
      "useEffect"
    ];
    const contentLower = content.toLowerCase();
    return uiKeywords.some((keyword) => contentLower.includes(keyword));
  }
  parseComponent(content, language, fileName) {
    const name = this.extractComponentName(content, fileName);
    const type = this.determineComponentType(content);
    const props = this.extractProps(content);
    const imports = this.extractImports(content);
    const exports = this.extractExports(content);
    const dependencies = this.extractDependencies(content);
    const framework = this.detectFrameworkFromContent(content);
    return {
      name,
      type,
      props,
      imports,
      exports,
      dependencies,
      framework,
      hasStyles: this.hasStyles(content),
      isResponsive: this.isResponsive(content),
      hasAccessibility: this.hasAccessibility(content),
      complexity: this.calculateComplexity(content)
    };
  }
  extractComponentName(content, fileName) {
    const functionMatch = content.match(/(?:function|const|class)\s+([A-Z][a-zA-Z0-9]*)/);
    if (functionMatch)
      return functionMatch[1];
    return fileName.replace(/\.(tsx?|jsx?|vue|svelte)$/, "");
  }
  determineComponentType(content) {
    if (content.includes("class ") && content.includes("extends"))
      return "class";
    if (content.includes("use") && content.match(/use[A-Z]/))
      return "hook";
    if (content.includes("function") || content.includes("const") || content.includes("=>"))
      return "functional";
    return "utility";
  }
  extractProps(content) {
    const props = [];
    const interfaceMatch = content.match(/interface\s+\w*Props\s*{([^}]*)}/s);
    if (interfaceMatch) {
      const propsContent = interfaceMatch[1];
      const propMatches = propsContent.match(/(\w+)(?:\?)?:/g);
      if (propMatches) {
        props.push(...propMatches.map((match) => match.replace(/[?:]/g, "")));
      }
    }
    const destructureMatch = content.match(/{\s*([^}]+)\s*}/);
    if (destructureMatch) {
      const destructured = destructureMatch[1].split(",").map((prop) => prop.trim().split(":")[0].trim());
      props.push(...destructured);
    }
    return [...new Set(props)];
  }
  extractImports(content) {
    const imports = [];
    const importMatches = content.match(/import\s+.*?from\s+['"]([^'"]+)['"]/g);
    if (importMatches) {
      for (const match of importMatches) {
        const moduleMatch = match.match(/from\s+['"]([^'"]+)['"]/);
        if (moduleMatch) {
          imports.push(moduleMatch[1]);
        }
      }
    }
    return imports;
  }
  extractExports(content) {
    const exports = [];
    const exportMatches = content.match(/export\s+(?:default\s+)?(?:function|const|class|interface|type)\s+(\w+)/g);
    if (exportMatches) {
      for (const match of exportMatches) {
        const nameMatch = match.match(/(?:function|const|class|interface|type)\s+(\w+)/);
        if (nameMatch) {
          exports.push(nameMatch[1]);
        }
      }
    }
    return exports;
  }
  extractDependencies(content) {
    const deps = [];
    const imports = this.extractImports(content);
    for (const imp of imports) {
      if (!imp.startsWith(".") && !imp.startsWith("/")) {
        deps.push(imp.split("/")[0]);
      }
    }
    return [...new Set(deps)];
  }
  detectFrameworkFromContent(content) {
    if (content.includes("React") || content.includes("jsx") || content.includes("useState"))
      return "React";
    if (content.includes("Vue") || content.includes("<template>"))
      return "Vue";
    if (content.includes("@Component") || content.includes("Angular"))
      return "Angular";
    if (content.includes("svelte") || content.includes("<script>"))
      return "Svelte";
    return "JavaScript";
  }
  hasStyles(content) {
    return content.includes("className") || content.includes("style=") || content.includes("styled") || content.includes("css`") || content.includes(".module.css");
  }
  isResponsive(content) {
    const responsiveKeywords = ["responsive", "mobile", "tablet", "desktop", "breakpoint", "media", "sm:", "md:", "lg:", "xl:"];
    return responsiveKeywords.some((keyword) => content.toLowerCase().includes(keyword));
  }
  hasAccessibility(content) {
    const a11yKeywords = ["aria-", "role=", "tabIndex", "alt=", "label", "accessibility", "screen reader"];
    return a11yKeywords.some((keyword) => content.toLowerCase().includes(keyword));
  }
  calculateComplexity(content) {
    const lines = content.split("\n").length;
    const cyclomaticComplexity = (content.match(/if|else|for|while|switch|case|\?|&&|\|\|/g) || []).length;
    if (lines > 200 || cyclomaticComplexity > 15)
      return "high";
    if (lines > 100 || cyclomaticComplexity > 8)
      return "medium";
    return "low";
  }
  detectDesignPatterns(components) {
    const patterns = [];
    if (components.some((c) => c.name.includes("Provider")))
      patterns.push("Provider Pattern");
    if (components.some((c) => c.name.includes("Hook")))
      patterns.push("Custom Hooks");
    if (components.some((c) => c.name.includes("HOC")))
      patterns.push("Higher-Order Components");
    if (components.some((c) => c.name.includes("Container")))
      patterns.push("Container/Presentational");
    return patterns;
  }
  hasAccessibilityFeatures(components, dependencies) {
    return components.some((c) => c.hasAccessibility) || dependencies.some((dep) => dep.includes("a11y") || dep.includes("accessibility"));
  }
  hasResponsiveDesign(components, dependencies) {
    return components.some((c) => c.isResponsive) || dependencies.includes("tailwindcss") || dependencies.includes("@media");
  }
};

// src/file-watcher.ts
var FileWatcher = class {
  constructor(extensionContext, vectorDb) {
    this.extensionContext = extensionContext;
    this.watchers = [];
    this.changeQueue = [];
    this.processingTimer = null;
    this.vectorDb = vectorDb;
    this.astAnalyzer = new ASTAnalyzer();
    this.initializeWatchers();
  }
  initializeWatchers() {
    const patterns = [
      "**/*.{tsx,jsx,ts,js}",
      "**/*.vue",
      "**/*.svelte",
      "**/*.css",
      "**/*.scss",
      "**/*.sass",
      "**/*.less",
      "**/package.json",
      "**/tsconfig.json",
      "**/tailwind.config.js",
      "**/next.config.js",
      "**/vite.config.js"
    ];
    for (const pattern of patterns) {
      const watcher = vscode2.workspace.createFileSystemWatcher(pattern);
      watcher.onDidCreate((uri) => this.handleFileChange("created", uri.fsPath));
      watcher.onDidChange((uri) => this.handleFileChange("modified", uri.fsPath));
      watcher.onDidDelete((uri) => this.handleFileChange("deleted", uri.fsPath));
      this.watchers.push(watcher);
    }
    vscode2.window.onDidChangeActiveTextEditor((editor) => {
      if (editor && this.isUIFile(editor.document)) {
        this.handleActiveFileChange(editor.document);
      }
    });
    vscode2.workspace.onDidSaveTextDocument((document) => {
      if (this.isUIFile(document)) {
        this.handleFileChange("modified", document.uri.fsPath);
      }
    });
  }
  handleFileChange(type, filePath) {
    if (this.shouldIgnoreFile(filePath)) {
      return;
    }
    const event = {
      type,
      filePath,
      timestamp: new Date()
    };
    this.changeQueue.push(event);
    this.scheduleProcessing();
  }
  scheduleProcessing() {
    if (this.processingTimer) {
      clearTimeout(this.processingTimer);
    }
    this.processingTimer = setTimeout(() => {
      this.processChangeQueue();
    }, 1e3);
  }
  async processChangeQueue() {
    if (this.changeQueue.length === 0)
      return;
    const events = [...this.changeQueue];
    this.changeQueue = [];
    for (const event of events) {
      try {
        await this.processFileChange(event);
      } catch (error) {
        console.error(`Error processing file change for ${event.filePath}:`, error);
      }
    }
  }
  async processFileChange(event) {
    switch (event.type) {
      case "created":
      case "modified":
        await this.updateFileContext(event.filePath);
        break;
      case "deleted":
        await this.removeFileContext(event.filePath);
        break;
    }
  }
  async updateFileContext(filePath) {
    try {
      const componentInfo = await this.astAnalyzer.analyzeFile(filePath);
      if (!componentInfo)
        return;
      const document = await vscode2.workspace.openTextDocument(filePath);
      const content = document.getText();
      const context = {
        filePath,
        content: content.substring(0, 1e4),
        language: document.languageId,
        framework: componentInfo.framework,
        dependencies: componentInfo.dependencies,
        imports: componentInfo.imports,
        exports: componentInfo.exports,
        components: [componentInfo.name],
        embedding: new Float32Array(384),
        semanticChunks: [],
        lastModified: new Date()
      };
      const existingContext = this.vectorDb.getProjectContext(filePath);
      if (existingContext) {
        Object.assign(existingContext, context);
      } else {
        this.vectorDb.addProjectContext(context);
      }
    } catch (error) {
      console.error(`Error updating context for ${filePath}:`, error);
    }
  }
  async removeFileContext(filePath) {
    console.log(`File deleted: ${filePath}`);
  }
  async handleActiveFileChange(document) {
    if (this.isUIFile(document)) {
      await this.updateFileContext(document.uri.fsPath);
    }
  }
  isUIFile(document) {
    const uiExtensions = [".tsx", ".jsx", ".ts", ".js", ".vue", ".svelte"];
    const uiLanguages = ["typescript", "javascript", "typescriptreact", "javascriptreact", "vue", "svelte"];
    return uiExtensions.some((ext) => document.fileName.endsWith(ext)) || uiLanguages.includes(document.languageId);
  }
  shouldIgnoreFile(filePath) {
    const ignorePaths = [
      "node_modules",
      ".git",
      "dist",
      "build",
      ".next",
      ".nuxt",
      "coverage",
      ".vscode",
      ".idea"
    ];
    return ignorePaths.some((ignore) => filePath.includes(ignore));
  }
  getRecentChanges(limit = 10) {
    return this.changeQueue.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime()).slice(0, limit);
  }
  async getProjectSummary() {
    const projectAnalysis = await this.astAnalyzer.analyzeProject();
    const recentChanges = this.changeQueue.filter(
      (event) => event.timestamp > new Date(Date.now() - 24 * 60 * 60 * 1e3)
    ).length;
    return {
      totalFiles: projectAnalysis.fileStructure.length,
      recentChanges,
      frameworks: [projectAnalysis.framework, projectAnalysis.stylingFramework],
      components: projectAnalysis.components.length
    };
  }
  async getContextForCurrentFile() {
    const activeEditor = vscode2.window.activeTextEditor;
    if (!activeEditor) {
      return { relatedFiles: [], suggestions: [] };
    }
    const currentFile = this.vectorDb.getProjectContext(activeEditor.document.uri.fsPath);
    const relatedFiles = [];
    const suggestions = [];
    if (currentFile) {
      suggestions.push(
        "Consider adding responsive design",
        "Add accessibility features",
        "Apply modern styling patterns"
      );
    }
    return {
      currentFile,
      relatedFiles,
      suggestions
    };
  }
  dispose() {
    for (const watcher of this.watchers) {
      watcher.dispose();
    }
    this.watchers = [];
    if (this.processingTimer) {
      clearTimeout(this.processingTimer);
      this.processingTimer = null;
    }
  }
};

// src/clean-view-provider.ts
var CleanUIOrbitViewProvider = class {
  constructor(context) {
    this.context = context;
    this.messages = [];
    this.isProcessing = false;
    this.vectorDb = new VectorDatabase(context);
    this.fileWatcher = new FileWatcher(context, this.vectorDb);
    this.astAnalyzer = new ASTAnalyzer();
  }
  resolveWebviewView(webviewView, _context, _token) {
    this.webView = webviewView;
    webviewView.webview.options = {
      enableScripts: true,
      localResourceRoots: [this.context.extensionUri]
    };
    webviewView.webview.html = this.getWebviewContent();
    webviewView.webview.onDidReceiveMessage(async (message) => {
      await this.handleWebviewMessage(message);
    });
    this.initializeServices();
  }
  async initializeServices() {
    try {
      await this.fileWatcher.start();
      this.addMessage({
        type: "status",
        content: "\u{1F680} UIOrbit initialized - Local codebase intelligence active",
        timestamp: new Date()
      });
    } catch (error) {
      this.addMessage({
        type: "error",
        content: `Failed to initialize UIOrbit: ${error}`,
        timestamp: new Date()
      });
    }
  }
  async handleWebviewMessage(message) {
    switch (message.type) {
      case "ui-request":
        await this.handleUIRequest(message.content);
        break;
      case "get-context":
        await this.sendProjectContext();
        break;
      case "clear-conversation":
        this.clearConversation();
        break;
      default:
        console.log("Unknown message type:", message.type);
    }
  }
  async handleUIRequest(prompt) {
    if (this.isProcessing) {
      this.addMessage({
        type: "error",
        content: "Please wait for the current request to complete.",
        timestamp: new Date()
      });
      return;
    }
    this.isProcessing = true;
    this.addMessage({
      type: "request",
      content: prompt,
      timestamp: new Date()
    });
    try {
      const context = await this.getRelevantContext(prompt);
      const response = await this.generateUIResponse(prompt, context);
      this.addMessage({
        type: "response",
        content: response,
        timestamp: new Date(),
        context
      });
    } catch (error) {
      this.addMessage({
        type: "error",
        content: `Error generating UI: ${error}`,
        timestamp: new Date()
      });
    } finally {
      this.isProcessing = false;
    }
  }
  async getRelevantContext(prompt) {
    const searchResults = await this.vectorDb.semanticSearch(prompt, 5);
    const activeEditor = vscode3.window.activeTextEditor;
    const currentFileContext = activeEditor ? this.vectorDb.getProjectContext(activeEditor.document.uri.fsPath) : null;
    const projectAnalysis = await this.astAnalyzer.analyzeProject();
    return {
      searchResults,
      currentFile: currentFileContext,
      project: {
        framework: projectAnalysis.framework,
        stylingFramework: projectAnalysis.stylingFramework,
        hasAccessibility: projectAnalysis.accessibility,
        isResponsive: projectAnalysis.responsive
      }
    };
  }
  async generateUIResponse(prompt, context) {
    const apiKey = vscode3.workspace.getConfiguration("uiorbit").get("apiKey");
    if (!apiKey) {
      return `
## \u26A0\uFE0F API Key Required

To use UIOrbit, please set your API key in VS Code settings:

1. Open Settings (Ctrl+,)
2. Search for "UIOrbit API Key"
3. Enter your API key from [UIOrbit Dashboard](https://uiorbit.dev/dashboard)

---

**Context-Aware Analysis:**
- Framework: ${context.project.framework}
- Styling: ${context.project.stylingFramework}
- Accessibility: ${context.project.hasAccessibility ? "\u2705" : "\u274C"}
- Responsive: ${context.project.isResponsive ? "\u2705" : "\u274C"}
- Relevant patterns found: ${context.searchResults.length}

**Your request:** ${prompt}
            `;
    }
    return `
## \u{1F3A8} Generated UI Component

\`\`\`tsx
interface ${this.extractComponentName(prompt)}Props {
  children?: React.ReactNode;
  className?: string;
}

export const ${this.extractComponentName(prompt)}: React.FC<${this.extractComponentName(prompt)}Props> = ({ 
  children, 
  className = '' 
}) => {
  return (
    <div className={\`modern-component \${className}\`}>
      {children}
    </div>
  );
};
\`\`\`

**Context Used:**
- Framework: ${context.project.framework}
- Styling: ${context.project.stylingFramework}
- Found ${context.searchResults.length} relevant patterns in your codebase

*This is a placeholder response. Connect your API key to get AI-generated components.*
        `;
  }
  extractComponentName(prompt) {
    const words = prompt.split(" ").filter((word) => word.length > 2);
    return words.length > 0 ? words[0].charAt(0).toUpperCase() + words[0].slice(1) : "Component";
  }
  async sendProjectContext() {
    const status = this.fileWatcher.getIndexingStatus();
    const trendingPatterns = this.vectorDb.getTrendingPatterns();
    this.webView?.webview.postMessage({
      type: "project-context",
      data: {
        indexingStatus: status,
        trendingPatterns: trendingPatterns.slice(0, 5),
        totalPatterns: trendingPatterns.length
      }
    });
  }
  addMessage(message) {
    this.messages.push(message);
    this.updateWebview();
  }
  clearConversation() {
    this.messages = [];
    this.updateWebview();
  }
  updateWebview() {
    if (this.webView) {
      this.webView.webview.postMessage({
        type: "update-messages",
        messages: this.messages
      });
    }
  }
  getWebviewContent() {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UIOrbit</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            font-size: var(--vscode-font-size);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            margin: 0;
            padding: 16px;
        }
        
        .header {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid var(--vscode-panel-border);
        }
        
        .logo {
            font-size: 18px;
            font-weight: bold;
            color: var(--vscode-textLink-foreground);
        }
        
        .status {
            margin-left: auto;
            font-size: 12px;
            color: var(--vscode-descriptionForeground);
        }
        
        .input-container {
            display: flex;
            gap: 8px;
            margin-bottom: 16px;
        }
        
        .input-field {
            flex: 1;
            padding: 8px 12px;
            background: var(--vscode-input-background);
            border: 1px solid var(--vscode-input-border);
            color: var(--vscode-input-foreground);
            border-radius: 4px;
        }
        
        .send-button {
            padding: 8px 16px;
            background: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .send-button:hover {
            background: var(--vscode-button-hoverBackground);
        }
        
        .send-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .messages {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .message {
            margin-bottom: 12px;
            padding: 8px;
            border-radius: 4px;
        }
        
        .message.request {
            background: var(--vscode-textBlockQuote-background);
            border-left: 3px solid var(--vscode-textLink-foreground);
        }
        
        .message.response {
            background: var(--vscode-editor-background);
            border: 1px solid var(--vscode-panel-border);
        }
        
        .message.error {
            background: var(--vscode-inputValidation-errorBackground);
            border-left: 3px solid var(--vscode-inputValidation-errorBorder);
        }
        
        .message.status {
            background: var(--vscode-inputValidation-infoBackground);
            border-left: 3px solid var(--vscode-inputValidation-infoBorder);
        }
        
        .timestamp {
            font-size: 11px;
            color: var(--vscode-descriptionForeground);
            margin-bottom: 4px;
        }
        
        .clear-button {
            padding: 4px 8px;
            background: var(--vscode-button-secondaryBackground);
            color: var(--vscode-button-secondaryForeground);
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
        
        pre {
            background: var(--vscode-textCodeBlock-background);
            padding: 8px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 13px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">\u{1F3A8} UIOrbit</div>
        <div class="status" id="status">Ready</div>
    </div>
    
    <div class="input-container">
        <input 
            type="text" 
            class="input-field" 
            id="promptInput" 
            placeholder="Describe the UI component you want to generate..."
            onkeypress="handleKeyPress(event)"
        />
        <button class="send-button" id="sendButton" onclick="sendMessage()">Generate</button>
        <button class="clear-button" onclick="clearConversation()">Clear</button>
    </div>
    
    <div class="messages" id="messages"></div>
    
    <script>
        const vscode = acquireVsCodeApi();
        let isProcessing = false;
        
        function handleKeyPress(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }
        
        function sendMessage() {
            const input = document.getElementById('promptInput');
            const prompt = input.value.trim();
            
            if (!prompt || isProcessing) return;
            
            input.value = '';
            isProcessing = true;
            updateSendButton();
            
            vscode.postMessage({
                type: 'ui-request',
                content: prompt
            });
        }
        
        function clearConversation() {
            vscode.postMessage({ type: 'clear-conversation' });
        }
        
        function updateSendButton() {
            const button = document.getElementById('sendButton');
            button.disabled = isProcessing;
            button.textContent = isProcessing ? 'Generating...' : 'Generate';
        }
        
        function updateMessages(messages) {
            const container = document.getElementById('messages');
            container.innerHTML = '';
            
            messages.forEach(message => {
                const div = document.createElement('div');
                div.className = \`message \${message.type}\`;
                
                const timestamp = document.createElement('div');
                timestamp.className = 'timestamp';
                timestamp.textContent = new Date(message.timestamp).toLocaleTimeString();
                
                const content = document.createElement('div');
                content.innerHTML = message.content.replace(/\\n/g, '<br>');
                
                div.appendChild(timestamp);
                div.appendChild(content);
                container.appendChild(div);
            });
            
            container.scrollTop = container.scrollHeight;
            isProcessing = false;
            updateSendButton();
        }
        
        // Handle messages from extension
        window.addEventListener('message', event => {
            const message = event.data;
            
            switch (message.type) {
                case 'update-messages':
                    updateMessages(message.messages);
                    break;
                case 'project-context':
                    document.getElementById('status').textContent = 
                        \`\${message.data.totalPatterns} patterns indexed\`;
                    break;
            }
        });
        
        // Request initial context
        vscode.postMessage({ type: 'get-context' });
    <\/script>
</body>
</html>
        `;
  }
  dispose() {
    this.fileWatcher.dispose();
  }
};

// src/extension.ts
var menuCommands = ["addStyling", "makeResponsive", "addAccessibility", "optimizeUI", "explainDesign", "generateVariants", "generateComponent", "trendingPatterns", "designSystem", "customUIPrompt"];
async function activate(context) {
  console.log("UIOrbit extension is activating...");
  const provider = new CleanUIOrbitViewProvider(context);
  const view = vscode4.window.registerWebviewViewProvider(
    "uiorbit.view",
    provider,
    {
      webviewOptions: {
        retainContextWhenHidden: true
      }
    }
  );
  const freeText = vscode4.commands.registerCommand("uiorbit.freeText", async () => {
    const value = await vscode4.window.showInputBox({
      prompt: "Describe the UI component you want to generate...",
      placeHolder: "e.g., responsive navigation bar with dark mode toggle"
    });
    if (value) {
      vscode4.commands.executeCommand("uiorbit.view.focus");
    }
  });
  const clearConversation = vscode4.commands.registerCommand("uiorbit.clearConversation", async () => {
    vscode4.commands.executeCommand("uiorbit.view.focus");
  });
  const configChanged = vscode4.workspace.onDidChangeConfiguration((e) => {
    if (e.affectsConfiguration("uiorbit.apiKey") || e.affectsConfiguration("uiorbit.enableLocalIndexing")) {
      console.log("UIOrbit configuration updated");
    }
  });
  const customUIPromptCommand = vscode4.commands.registerCommand("uiorbit.customUIPrompt", async () => {
    const editor = vscode4.window.activeTextEditor;
    if (!editor) {
      return;
    }
    const selection = editor.document.getText(editor.selection);
    let dismissed = false;
    if (selection) {
      await vscode4.window.showInputBox({
        title: "Custom UI Prompt",
        prompt: "Enter your custom UI/UX prompt. e.g., 'Make this component more accessible'",
        ignoreFocusOut: true,
        placeHolder: "Describe what you want to do with this UI component...",
        value: customUIPromptPrefix
      }).then((value) => {
        if (!value) {
          dismissed = true;
          return;
        }
        customUIPromptPrefix = value.trim() || "";
        context.globalState.update("uiorbit-custom-prompt", customUIPromptPrefix);
      });
      if (!dismissed && customUIPromptPrefix?.length > 0) {
        provider?.sendApiRequest(customUIPromptPrefix, { command: "customUIPrompt", code: selection });
      }
    }
  });
  const generateComponentCommand = vscode4.commands.registerCommand(`uiorbit.generateComponent`, () => {
    const editor = vscode4.window.activeTextEditor;
    if (!editor) {
      return;
    }
    const selection = editor.document.getText(editor.selection);
    if (selection) {
      provider?.sendApiRequest(selection, { command: "generateComponent", language: editor.document.languageId });
    }
  });
  const registeredCommands = menuCommands.filter((command) => command !== "customUIPrompt" && command !== "generateComponent").map((command) => vscode4.commands.registerCommand(`uiorbit.${command}`, () => {
    const prompt = vscode4.workspace.getConfiguration("uiorbit").get(`promptPrefix.${command}`);
    const editor = vscode4.window.activeTextEditor;
    if (!editor) {
      return;
    }
    const selection = editor.document.getText(editor.selection);
    if (selection && prompt) {
      provider?.sendApiRequest(prompt, { command, code: selection, language: editor.document.languageId });
    }
  }));
  const installShadcn = vscode4.commands.registerCommand("uiorbit.installShadcn", async () => {
    await provider?.installComponentLibrary("shadcn");
  });
  const installMUI = vscode4.commands.registerCommand("uiorbit.installMUI", async () => {
    await provider?.installComponentLibrary("mui");
  });
  const installAntd = vscode4.commands.registerCommand("uiorbit.installAntd", async () => {
    await provider?.installComponentLibrary("antd");
  });
  const installChakra = vscode4.commands.registerCommand("uiorbit.installChakra", async () => {
    await provider?.installComponentLibrary("chakra");
  });
  const generateFromTemplate = vscode4.commands.registerCommand("uiorbit.generateFromTemplate", async () => {
    await provider?.generateFromTemplate();
  });
  const showEnterpriseDashboard = vscode4.commands.registerCommand("uiorbit.showEnterpriseDashboard", () => {
    provider?.showEnterpriseDashboard();
  });
  const startCollaboration = vscode4.commands.registerCommand("uiorbit.startCollaboration", async () => {
    await provider?.startCollaboration();
  });
  const showCollaborationPanel = vscode4.commands.registerCommand("uiorbit.showCollaborationPanel", () => {
    provider?.showCollaborationPanel();
  });
  context.subscriptions.push(view, freeText, resetThread, exportConversation, clearSession, configChanged, customUIPromptCommand, generateComponentCommand, ...registeredCommands, installShadcn, installMUI, installAntd, installChakra, generateFromTemplate, showEnterpriseDashboard, startCollaboration, showCollaborationPanel);
  const setContext = () => {
    menuCommands.forEach((command) => {
      if (command === "generateComponent") {
        let generateComponentEnabled = !!vscode4.workspace.getConfiguration("uiorbit").get("generateComponent-enabled");
        vscode4.commands.executeCommand("setContext", "generateComponent-enabled", generateComponentEnabled);
      } else {
        const enabled = !!vscode4.workspace.getConfiguration("uiorbit.promptPrefix").get(`${command}-enabled`);
        vscode4.commands.executeCommand("setContext", `${command}-enabled`, enabled);
      }
    });
  };
  setContext();
}
function deactivate() {
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  activate,
  deactivate
});
//# sourceMappingURL=extension.js.map
