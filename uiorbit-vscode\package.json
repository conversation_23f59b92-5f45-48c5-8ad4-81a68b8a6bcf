{"name": "uiorbit-vscode", "publisher": "UIOrbit", "displayName": "UIOrbit - AI-Powered UI/UX Assistant", "icon": "images/ai-logo.jpg", "description": "AI-powered VS Code extension for generating modern UI components, trending designs, and responsive layouts with context-aware intelligence", "version": "1.0.0", "aiKey": "", "repository": {"url": "https://github.com/UIOrbit/uiorbit-vscode"}, "engines": {"vscode": "^1.73.0"}, "categories": ["Other", "Snippets", "Formatters", "Programming Languages", "Machine Learning"], "keywords": ["ui", "ux", "design", "components", "react", "vue", "angular", "svelte", "tailwind", "css", "html", "frontend", "responsive", "accessibility", "design-system", "ai", "assistant", "generation", "trends"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"menus": {"editor/context": [{"command": "uiorbit.generateComponent", "group": "uiorbit@1", "when": "editorHasSelection && generateComponent-enabled"}, {"command": "uiorbit.addStyling", "group": "uiorbit@2", "when": "editorHasSelection && addStyling-enabled"}, {"command": "uiorbit.makeResponsive", "group": "uiorbit@3", "when": "editorHasSelection && makeResponsive-enabled"}, {"command": "uiorbit.addAccessibility", "group": "uiorbit@4", "when": "editorHasSelection && addAccessibility-enabled"}, {"command": "uiorbit.optimizeUI", "group": "uiorbit@5", "when": "editorHasSelection && optimizeUI-enabled"}, {"command": "uiorbit.explainDesign", "group": "uiorbit@6", "when": "editorHasSelection && explainDesign-enabled"}, {"command": "uiorbit.generateVariants", "group": "uiorbit@7", "when": "editorHasSelection && generateVariants-enabled"}, {"command": "uiorbit.customUIPrompt", "group": "uiorbit@8", "when": "editorHasSelection && customUIPrompt-enabled"}, {"command": "uiorbit.trendingPatterns", "group": "uiorbit@9", "when": "editorHasSelection && trendingPatterns-enabled"}, {"command": "uiorbit.designSystem", "group": "uiorbit@10", "when": "editorHasSelection && designSystem-enabled"}]}, "keybindings": [{"command": "uiorbit.generateComponent", "key": "ctrl+shift+u", "mac": "cmd+shift+u", "when": "editorHasSelection"}, {"command": "uiorbit.addStyling", "key": "ctrl+k ctrl+shift+1", "mac": "cmd+k cmd+shift+1", "when": "editorHasSelection"}, {"command": "uiorbit.makeResponsive", "key": "ctrl+k ctrl+shift+2", "mac": "cmd+k cmd+shift+2", "when": "editorHasSelection"}, {"command": "uiorbit.addAccessibility", "key": "ctrl+k ctrl+shift+3", "mac": "cmd+k cmd+shift+3", "when": "editorHasSelection"}, {"command": "uiorbit.optimizeUI", "key": "ctrl+k ctrl+shift+4", "mac": "cmd+k cmd+shift+4", "when": "editorHasSelection"}, {"command": "uiorbit.explainDesign", "key": "ctrl+k ctrl+shift+5", "mac": "cmd+k cmd+shift+5", "when": "editorHasSelection"}, {"command": "uiorbit.generateVariants", "key": "ctrl+k ctrl+shift+6", "mac": "cmd+k cmd+shift+6", "when": "editorHasSelection"}, {"command": "uiorbit.customUIPrompt", "key": "ctrl+k ctrl+shift+7", "mac": "cmd+k cmd+shift+7", "when": "editorHasSelection"}, {"command": "uiorbit.trendingPatterns", "key": "ctrl+k ctrl+shift+8", "mac": "cmd+k cmd+shift+8", "when": "editorHasSelection"}, {"command": "uiorbit.designSystem", "key": "ctrl+k ctrl+shift+9", "mac": "cmd+k cmd+shift+9", "when": "editorHasSelection"}], "commands": [{"command": "uiorbit.freeText", "title": "UIOrbit: Ask anything about UI/UX"}, {"command": "uiorbit.clearSession", "title": "UIOrbit: Reset session"}, {"command": "uiorbit.generateComponent", "title": "UIOrbit: Generate UI Component", "enablement": "editorHasSelection"}, {"command": "uiorbit.addStyling", "title": "UIOrbit: <PERSON><PERSON>", "enablement": "editorHasSelection"}, {"command": "uiorbit.makeResponsive", "title": "UIOrbit: Make Responsive", "enablement": "editorHasSelection"}, {"command": "uiorbit.addAccessibility", "title": "UIOrbit: Add Accessibility", "enablement": "editorHasSelection"}, {"command": "uiorbit.optimizeUI", "title": "UIOrbit: Optimize UI", "enablement": "editorHasSelection"}, {"command": "uiorbit.explainDesign", "title": "UIOrbit: Explain Design", "enablement": "editorHasSelection"}, {"command": "uiorbit.generateVariants", "title": "UIOrbit: Generate Variants", "enablement": "editorHasSelection"}, {"command": "uiorbit.customUIPrompt", "title": "UIOrbit: Custom UI Prompt", "enablement": "editorHasSelection"}, {"command": "uiorbit.trendingPatterns", "title": "UIOrbit: <PERSON><PERSON><PERSON>", "enablement": "editorHasSelection"}, {"command": "uiorbit.designSystem", "title": "UIOrbit: Design System", "enablement": "editorHasSelection"}, {"command": "uiorbit.clearConversation", "title": "UIOrbit: Clear conversation"}, {"command": "uiorbit.exportConversation", "title": "UIOrbit: Export conversation"}, {"command": "uiorbit.installShadcn", "title": "UIOrbit: Install Shadcn/UI"}, {"command": "uiorbit.installMUI", "title": "UIOrbit: Install Material-UI"}, {"command": "uiorbit.installAntd", "title": "UIOrbit: Install Ant Design"}, {"command": "uiorbit.installChakra", "title": "UIOrbit: Install Chakra UI"}, {"command": "uiorbit.generateFromTemplate", "title": "UIOrbit: Generate from Template"}, {"command": "uiorbit.showEnterpriseDashboard", "title": "UIOrbit: Enterprise Dashboard"}, {"command": "uiorbit.startCollaboration", "title": "UIOrbit: Start Collaboration"}, {"command": "uiorbit.showCollaborationPanel", "title": "UIOrbit: Collaboration Panel"}], "viewsContainers": {"activitybar": [{"id": "uiorbit-view-container", "title": "UIOrbit", "icon": "images/uiorbit-logo.svg"}]}, "views": {"uiorbit-view-container": [{"type": "webview", "id": "uiorbit.view", "name": "UI Assistant"}]}, "configuration": {"title": "UIOrbit", "properties": {"uiorbit.apiKey": {"type": "string", "markdownDescription": "UIOrbit API key for accessing AI services. [Get your API Key from UIOrbit Dashboard](https://uiorbit.dev/dashboard). \n\n**Required for UIOrbit to function.**", "order": 1}, "uiorbit.enableLocalIndexing": {"type": "boolean", "default": true, "markdownDescription": "Enable local codebase indexing for context-aware UI generation. All processing happens locally for privacy.", "order": 2}, "uiorbit.framework": {"type": "string", "enum": ["React", "<PERSON><PERSON>", "Angular", "Svelte", "HTML/CSS", "Auto-detect"], "default": "Auto-detect", "markdownDescription": "Choose your preferred UI framework for component generation.", "order": 3}, "uiorbit.stylingFramework": {"type": "string", "enum": ["Tailwind CSS", "CSS Modules", "Styled Components", "SCSS/SASS", "Vanilla CSS", "Auto-detect"], "default": "Auto-detect", "markdownDescription": "Choose your preferred styling approach.", "order": 4}, "uiorbit.designSystem": {"type": "string", "enum": ["Shadcn/UI", "Material Design", "Ant Design", "Chakra UI", "Bootstrap", "Custom", "None"], "default": "Shadcn/UI", "markdownDescription": "Choose a design system to follow for component generation.", "order": 5}, "uiorbit.responsiveBreakpoints": {"type": "object", "default": {"mobile": "640px", "tablet": "768px", "desktop": "1024px", "wide": "1280px"}, "description": "Define custom responsive breakpoints for your project.", "order": 5}, "uiorbit.accessibilityLevel": {"type": "string", "enum": ["WCAG AA", "WCAG AAA", "Basic", "None"], "default": "WCAG AA", "markdownDescription": "Choose the accessibility compliance level for generated components.", "order": 6}, "uiorbit.generateComponent-enabled": {"type": "boolean", "default": true, "description": "Enable the component generation context menu item", "order": 7}, "uiorbit.promptPrefix.addStyling": {"type": "string", "default": "Add modern styling to the following UI component", "description": "The prompt prefix used for adding styling to selected UI code", "order": 8}, "uiorbit.promptPrefix.addStyling-enabled": {"type": "boolean", "default": true, "description": "Enable the styling enhancement context menu item", "order": 9}, "uiorbit.promptPrefix.makeResponsive": {"type": "string", "default": "Make the following UI component responsive for mobile, tablet, and desktop", "description": "The prompt prefix used for making components responsive", "order": 10}, "uiorbit.promptPrefix.makeResponsive-enabled": {"type": "boolean", "default": true, "description": "Enable the responsive design context menu item", "order": 11}, "uiorbit.promptPrefix.addAccessibility": {"type": "string", "default": "Add accessibility features to the following UI component", "description": "The prompt prefix used for adding accessibility features", "order": 12}, "uiorbit.promptPrefix.addAccessibility-enabled": {"type": "boolean", "default": true, "description": "Enable the accessibility enhancement context menu item", "order": 13}, "uiorbit.promptPrefix.explainDesign": {"type": "string", "default": "Explain the design patterns and UI principles in the following component", "description": "The prompt prefix used for explaining UI design decisions", "order": 14}, "uiorbit.promptPrefix.explainDesign-enabled": {"type": "boolean", "default": true, "description": "Enable the design explanation context menu item", "order": 15}, "uiorbit.promptPrefix.optimizeUI": {"type": "string", "default": "Optimize the following UI component for performance and user experience", "description": "The prompt prefix used for optimizing UI components", "order": 16}, "uiorbit.promptPrefix.optimizeUI-enabled": {"type": "boolean", "default": true, "description": "Enable the UI optimization context menu item", "order": 17}, "uiorbit.promptPrefix.generateVariants": {"type": "string", "default": "Generate design variants for the following UI component", "description": "The prompt prefix used for generating component variants", "order": 18}, "uiorbit.promptPrefix.generateVariants-enabled": {"type": "boolean", "default": true, "description": "Enable the variant generation context menu item", "order": 19}, "uiorbit.promptPrefix.trendingPatterns": {"type": "string", "default": "Apply trending UI patterns to the following component", "description": "The prompt prefix used for applying trending UI patterns", "order": 20}, "uiorbit.promptPrefix.trendingPatterns-enabled": {"type": "boolean", "default": true, "description": "Enable the trending patterns context menu item", "order": 21}, "uiorbit.promptPrefix.designSystem": {"type": "string", "default": "Apply design system principles to the following component", "description": "The prompt prefix used for design system integration", "order": 22}, "uiorbit.promptPrefix.designSystem-enabled": {"type": "boolean", "default": true, "description": "Enable the design system context menu item", "order": 23}, "uiorbit.promptPrefix.customUIPrompt-enabled": {"type": "boolean", "default": true, "description": "Enable the custom UI prompt context menu item", "order": 24}, "uiorbit.includeContext": {"type": "boolean", "default": true, "description": "Include project context (nearby files, imports, etc.) when generating UI components.", "order": 36}, "uiorbit.response.showNotification": {"type": "boolean", "default": false, "description": "Show notification when UIOrbit completes a UI generation task.", "order": 37}, "uiorbit.response.autoScroll": {"type": "boolean", "default": true, "description": "Automatically scroll to the bottom when new responses are added to the conversation.", "order": 38}, "uiorbit.telemetry.disable": {"type": "boolean", "default": false, "markdownDescription": "Disable telemetry collection. UIOrbit respects VS Code's telemetry settings. We only collect usage statistics to improve the extension.", "order": 39}}}}, "scripts": {"vscode:prepublish": "rimraf out && npm run esbuild-base -- --minify", "esbuild-base": "esbuild ./src/extension.ts --bundle --outfile=out/extension.js --external:vscode --format=cjs --platform=node", "build": "npm run -S esbuild-base -- --sourcemap", "watch": "npm run -S esbuild-base -- --sourcemap --watch", "fmt": "prettier --write \"src/**/*.ts\"&& npm run test -- --fix", "test": "eslint src --ext ts && tsc --noEmit"}, "devDependencies": {"@types/glob": "^8.0.0", "@types/isomorphic-fetch": "^0.0.36", "@types/mocha": "^10.0.1", "@types/node": "16.x", "@types/uuid": "^9.0.0", "@types/vscode": "^1.73.0", "@types/vscode-webview": "^1.57.0", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "@vscode/test-electron": "^2.2.0", "esbuild": "^0.15.18", "eslint": "^8.28.0", "glob": "^8.0.3", "mocha": "^10.1.0", "ts-loader": "^9.4.1", "typescript": "^4.9.3"}, "dependencies": {"@types/better-sqlite3": "^7.6.13", "better-sqlite3": "^9.6.0", "chokidar": "^3.6.0", "delay": "^5.0.0", "eventsource-parser": "^0.1.0", "gpt3-tokenizer": "^1.1.5", "isomorphic-fetch": "^3.0.0", "keyv": "^4.5.2", "openai": "^4.0.0", "p-timeout": "^6.1.1", "quick-lru": "^6.1.1", "remark": "^14.0.2", "sqlite-vss": "^0.1.2", "strip-markdown": "^5.0.0", "uuid": "^9.0.0"}, "resolutions": {"clone-deep": "^4.0.1"}}