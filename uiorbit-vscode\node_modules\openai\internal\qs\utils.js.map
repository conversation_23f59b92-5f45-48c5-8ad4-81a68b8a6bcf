{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/internal/qs/utils.ts"], "names": [], "mappings": ";;;AAAA,0CAAoC;AAGpC,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC;AAC5C,MAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC;AAE/B,MAAM,SAAS,GAAG,CAAC,GAAG,EAAE;IACtB,MAAM,KAAK,GAAG,EAAE,CAAC;IACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,EAAE,CAAC,EAAE;QAC5B,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC;KACxE;IAED,OAAO,KAAK,CAAC;AACf,CAAC,CAAC,EAAE,CAAC;AAEL,SAAS,aAAa,CAAgC,KAAsC;IAC1F,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;QACvB,MAAM,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;QACzB,IAAI,CAAC,IAAI;YAAE,SAAS;QAEpB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEhC,IAAI,QAAQ,CAAC,GAAG,CAAC,EAAE;YACjB,MAAM,SAAS,GAAc,EAAE,CAAC;YAEhC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBACnC,IAAI,OAAO,GAAG,CAAC,CAAC,CAAC,KAAK,WAAW,EAAE;oBACjC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;iBACxB;aACF;YAED,aAAa;YACb,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC;SACjC;KACF;AACH,CAAC;AAED,SAAS,eAAe,CAAC,MAAa,EAAE,OAAkC;IACxE,MAAM,GAAG,GAAG,OAAO,IAAI,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACvE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;QACtC,IAAI,OAAO,MAAM,CAAC,CAAC,CAAC,KAAK,WAAW,EAAE;YACpC,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;SACpB;KACF;IAED,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAgB,KAAK,CACnB,MAAW,EACX,MAAW,EACX,UAAiE,EAAE;IAEnE,IAAI,CAAC,MAAM,EAAE;QACX,OAAO,MAAM,CAAC;KACf;IAED,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;QAC9B,IAAI,QAAQ,CAAC,MAAM,CAAC,EAAE;YACpB,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACrB;aAAM,IAAI,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;YAC/C,IACE,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY,IAAI,OAAO,CAAC,eAAe,CAAC,CAAC;gBAC9D,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,EACnC;gBACA,MAAM,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;aACvB;SACF;aAAM;YACL,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;SACzB;QAED,OAAO,MAAM,CAAC;KACf;IAED,IAAI,CAAC,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;QACzC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;KAChC;IAED,IAAI,WAAW,GAAG,MAAM,CAAC;IACzB,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;QACzC,aAAa;QACb,WAAW,GAAG,eAAe,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;KAChD;IAED,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC,MAAM,CAAC,EAAE;QACxC,MAAM,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;YAC9B,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE;gBACvB,MAAM,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;gBAC7B,IAAI,UAAU,IAAI,OAAO,UAAU,KAAK,QAAQ,IAAI,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;oBACpF,MAAM,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,UAAU,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;iBAC9C;qBAAM;oBACL,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;iBACnB;aACF;iBAAM;gBACL,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;aAClB;QACH,CAAC,CAAC,CAAC;QACH,OAAO,MAAM,CAAC;KACf;IAED,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,UAAU,GAAG,EAAE,GAAG;QAClD,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;QAE1B,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE;YACtB,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;SAC5C;aAAM;YACL,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;SAClB;QACD,OAAO,GAAG,CAAC;IACb,CAAC,EAAE,WAAW,CAAC,CAAC;AAClB,CAAC;AA9DD,sBA8DC;AAED,SAAgB,oBAAoB,CAAC,MAAW,EAAE,MAAW;IAC3D,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,UAAU,GAAG,EAAE,GAAG;QAClD,GAAG,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;QACvB,OAAO,GAAG,CAAC;IACb,CAAC,EAAE,MAAM,CAAC,CAAC;AACb,CAAC;AALD,oDAKC;AAED,SAAgB,MAAM,CAAC,GAAW,EAAE,CAAM,EAAE,OAAe;IACzD,MAAM,cAAc,GAAG,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAC/C,IAAI,OAAO,KAAK,YAAY,EAAE;QAC5B,gDAAgD;QAChD,OAAO,cAAc,CAAC,OAAO,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAAC;KAC3D;IACD,QAAQ;IACR,IAAI;QACF,OAAO,kBAAkB,CAAC,cAAc,CAAC,CAAC;KAC3C;IAAC,OAAO,CAAC,EAAE;QACV,OAAO,cAAc,CAAC;KACvB;AACH,CAAC;AAZD,wBAYC;AAED,MAAM,KAAK,GAAG,IAAI,CAAC;AAEZ,MAAM,MAAM,GAML,CAAC,GAAG,EAAE,eAAe,EAAE,OAAO,EAAE,KAAK,EAAE,MAAc,EAAE,EAAE;IACrE,0FAA0F;IAC1F,8DAA8D;IAC9D,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;QACpB,OAAO,GAAG,CAAC;KACZ;IAED,IAAI,MAAM,GAAG,GAAG,CAAC;IACjB,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;QAC3B,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;KAC9C;SAAM,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;QAClC,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;KACtB;IAED,IAAI,OAAO,KAAK,YAAY,EAAE;QAC5B,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,iBAAiB,EAAE,UAAU,EAAE;YAC3D,OAAO,QAAQ,GAAG,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC;QACtD,CAAC,CAAC,CAAC;KACJ;IAED,IAAI,GAAG,GAAG,EAAE,CAAC;IACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,IAAI,KAAK,EAAE;QAC7C,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,IAAI,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QAC7E,MAAM,GAAG,GAAG,EAAE,CAAC;QAEf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YACvC,IAAI,CAAC,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAC9B,IACE,CAAC,KAAK,IAAI,IAAI,IAAI;gBAClB,CAAC,KAAK,IAAI,IAAI,IAAI;gBAClB,CAAC,KAAK,IAAI,IAAI,IAAI;gBAClB,CAAC,KAAK,IAAI,IAAI,IAAI;gBAClB,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,MAAM;gBAClC,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,MAAM;gBAClC,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,MAAM;gBAClC,CAAC,MAAM,KAAK,iBAAO,IAAI,CAAC,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,MAAM;cACzD;gBACA,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBACpC,SAAS;aACV;YAED,IAAI,CAAC,GAAG,IAAI,EAAE;gBACZ,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;gBAC/B,SAAS;aACV;YAED,IAAI,CAAC,GAAG,KAAK,EAAE;gBACb,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,SAAS,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAE,GAAG,SAAS,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;gBAC7E,SAAS;aACV;YAED,IAAI,CAAC,GAAG,MAAM,IAAI,CAAC,IAAI,MAAM,EAAE;gBAC7B,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC;oBACb,SAAS,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAE,GAAG,SAAS,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,SAAS,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;gBACpG,SAAS;aACV;YAED,CAAC,IAAI,CAAC,CAAC;YACP,CAAC,GAAG,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;YAEtE,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC;gBACb,SAAS,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAE;oBAC5B,SAAS,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;oBACpC,SAAS,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;oBACnC,SAAS,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;SAChC;QAED,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;KACrB;IAED,OAAO,GAAG,CAAC;AACb,CAAC,CAAC;AA7EW,QAAA,MAAM,UA6EjB;AAEF,SAAgB,OAAO,CAAC,KAAU;IAChC,MAAM,KAAK,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;IACjD,MAAM,IAAI,GAAG,EAAE,CAAC;IAEhB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;QACrC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QACtB,aAAa;QACb,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEhC,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YACpC,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAE,CAAC;YACrB,MAAM,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;YACrB,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,KAAK,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;gBACvE,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;gBACpC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aAChB;SACF;KACF;IAED,aAAa,CAAC,KAAK,CAAC,CAAC;IAErB,OAAO,KAAK,CAAC;AACf,CAAC;AAvBD,0BAuBC;AAED,SAAgB,SAAS,CAAC,GAAQ;IAChC,OAAO,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,iBAAiB,CAAC;AACnE,CAAC;AAFD,8BAEC;AAED,SAAgB,SAAS,CAAC,GAAQ;IAChC,IAAI,CAAC,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;QACnC,OAAO,KAAK,CAAC;KACd;IAED,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,WAAW,IAAI,GAAG,CAAC,WAAW,CAAC,QAAQ,IAAI,GAAG,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;AAC1F,CAAC;AAND,8BAMC;AAED,SAAgB,OAAO,CAAC,CAAM,EAAE,CAAM;IACpC,OAAO,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACzB,CAAC;AAFD,0BAEC;AAED,SAAgB,SAAS,CAAI,GAAQ,EAAE,EAAe;IACpD,IAAI,QAAQ,CAAC,GAAG,CAAC,EAAE;QACjB,MAAM,MAAM,GAAG,EAAE,CAAC;QAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;YACtC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC;SAC1B;QACD,OAAO,MAAM,CAAC;KACf;IACD,OAAO,EAAE,CAAC,GAAG,CAAC,CAAC;AACjB,CAAC;AATD,8BASC"}