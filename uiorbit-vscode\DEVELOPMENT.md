# UIOrbit Development Guide

This guide covers everything you need to know about developing, building, and contributing to UIOrbit.

## 🚀 Quick Start

### Prerequisites
- **Node.js** 16+ and npm
- **VS Code** 1.73.0 or higher
- **TypeScript** knowledge
- **OpenAI API Key** for testing

### Setup Development Environment

1. **Clone the repository**
   ```bash
   git clone https://github.com/UIOrbit/uiorbit-vscode.git
   cd uiorbit-vscode
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Open in VS Code**
   ```bash
   code .
   ```

4. **Start development**
   - Press `F5` to launch Extension Development Host
   - Or use `Run > Start Debugging`

## 🏗️ Architecture Overview

UIOrbit follows a modular architecture with clear separation of concerns:

```
src/
├── extension.ts              # Main extension entry point
├── uiorbit-view-provider.ts  # Main webview provider
├── ui-prompts.ts            # UI-specific prompt templates
├── ui-trends.ts             # Current UI trends database
├── vector-database.ts       # Local pattern storage
├── ast-analyzer.ts          # Code analysis service
├── file-watcher.ts          # File system monitoring
├── types.ts                 # TypeScript type definitions
└── test/                    # Test suite
    └── extension.test.ts    # Main test file
```

### Core Components

#### 1. **UIOrbitViewProvider** (`uiorbit-view-provider.ts`)
- Main extension logic and webview management
- Handles AI API communication
- Manages user interactions and responses

#### 2. **UIPromptTemplates** (`ui-prompts.ts`)
- Specialized prompts for different UI tasks
- Context-aware prompt generation
- Framework-specific optimizations

#### 3. **VectorDatabase** (`vector-database.ts`)
- Local storage for UI patterns
- Component similarity matching
- Usage analytics and recommendations

#### 4. **ASTAnalyzer** (`ast-analyzer.ts`)
- Project structure analysis
- Framework and dependency detection
- Component complexity assessment

#### 5. **FileWatcher** (`file-watcher.ts`)
- Real-time file system monitoring
- Context updates on file changes
- Project state synchronization

## 🛠️ Development Workflow

### Building the Extension

```bash
# Compile TypeScript
npm run compile

# Watch mode for development
npm run watch

# Package for distribution
npm run package
```

### Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run specific test file
npm test -- --grep "UIPromptTemplates"
```

### Debugging

1. **Extension Host Debugging**
   - Set breakpoints in TypeScript files
   - Press `F5` to start debugging
   - Use the Extension Development Host window

2. **Webview Debugging**
   - Right-click in webview → "Inspect Element"
   - Use browser dev tools for HTML/CSS/JS

3. **Logging**
   ```typescript
   console.log('Debug info');  // Shows in Extension Host console
   this.logEvent('event-name', { data: 'value' });  // Custom logging
   ```

## 🧪 Testing Strategy

### Unit Tests
- Test individual functions and classes
- Mock external dependencies
- Focus on business logic

### Integration Tests
- Test VS Code API integration
- Verify command registration
- Test configuration handling

### Manual Testing Checklist
- [ ] Extension loads without errors
- [ ] All commands are registered
- [ ] Webview displays correctly
- [ ] API key configuration works
- [ ] UI generation produces valid code
- [ ] Context analysis functions properly

## 📝 Code Style & Standards

### TypeScript Guidelines
- Use strict type checking
- Prefer interfaces over types for objects
- Use async/await over Promises
- Add JSDoc comments for public APIs

### Naming Conventions
- **Files**: kebab-case (`ui-prompts.ts`)
- **Classes**: PascalCase (`UIOrbitViewProvider`)
- **Functions**: camelCase (`generateComponent`)
- **Constants**: UPPER_SNAKE_CASE (`UI_TRENDS_2025`)

### Code Organization
```typescript
// 1. Imports (external first, then internal)
import * as vscode from 'vscode';
import { UIPromptTemplates } from './ui-prompts';

// 2. Types and interfaces
interface ComponentInfo {
  name: string;
  type: string;
}

// 3. Constants
const DEFAULT_TIMEOUT = 5000;

// 4. Class definition
export class MyClass {
  // Properties first
  private property: string;
  
  // Constructor
  constructor() {}
  
  // Public methods
  public method() {}
  
  // Private methods
  private helper() {}
}
```

## 🔧 Configuration Management

### Adding New Settings

1. **Update package.json**
   ```json
   {
     "configuration": {
       "properties": {
         "uiorbit.newSetting": {
           "type": "string",
           "default": "defaultValue",
           "description": "Description of the setting"
         }
       }
     }
   }
   ```

2. **Access in code**
   ```typescript
   const setting = vscode.workspace.getConfiguration("uiorbit").get("newSetting");
   ```

3. **Handle changes**
   ```typescript
   vscode.workspace.onDidChangeConfiguration(e => {
     if (e.affectsConfiguration('uiorbit.newSetting')) {
       // Handle the change
     }
   });
   ```

## 🎨 Adding New UI Commands

### 1. Register Command
```typescript
// In extension.ts
const newCommand = vscode.commands.registerCommand("uiorbit.newCommand", () => {
  const editor = vscode.window.activeTextEditor;
  if (!editor) return;
  
  const selection = editor.document.getText(editor.selection);
  if (selection) {
    provider?.sendApiRequest(prompt, { 
      command: "newCommand", 
      code: selection 
    });
  }
});
```

### 2. Add to package.json
```json
{
  "commands": [
    {
      "command": "uiorbit.newCommand",
      "title": "UIOrbit: New Command",
      "enablement": "editorHasSelection"
    }
  ]
}
```

### 3. Create Prompt Template
```typescript
// In ui-prompts.ts
static newCommand(context: UIPromptContext): string {
  return `Your new command prompt template here...`;
}
```

## 🔍 Debugging Common Issues

### Extension Won't Load
- Check `package.json` syntax
- Verify all dependencies are installed
- Look for TypeScript compilation errors

### Commands Not Working
- Ensure commands are registered in `package.json`
- Check command enablement conditions
- Verify command handler implementation

### API Errors
- Validate API key configuration
- Check network connectivity
- Review API request format

### Performance Issues
- Profile with VS Code's built-in profiler
- Check for memory leaks in file watchers
- Optimize vector database queries

## 📦 Building for Production

### Pre-release Checklist
- [ ] All tests pass
- [ ] No TypeScript errors
- [ ] Update version in `package.json`
- [ ] Update `CHANGELOG.md`
- [ ] Test in clean VS Code environment

### Packaging
```bash
# Install vsce if not already installed
npm install -g vsce

# Package the extension
vsce package

# This creates uiorbit-vscode-x.x.x.vsix
```

### Publishing
```bash
# Login to marketplace
vsce login UIOrbit

# Publish new version
vsce publish

# Or publish specific version
vsce publish 1.0.1
```

## 🤝 Contributing Guidelines

### Pull Request Process
1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Make changes and add tests
4. Ensure all tests pass
5. Update documentation
6. Submit pull request

### Commit Message Format
```
type(scope): description

[optional body]

[optional footer]
```

Types: `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`

Example:
```
feat(prompts): add new responsive design template

Add specialized prompt for generating responsive layouts
with mobile-first approach and modern CSS Grid.

Closes #123
```

## 🔮 Future Development

### Planned Features
- **Figma Integration**: Import/export designs
- **Component Library**: Shared component marketplace
- **Advanced AI Models**: Specialized UI models
- **Team Collaboration**: Shared patterns and styles

### Architecture Improvements
- **Plugin System**: Third-party integrations
- **Better Caching**: Improved performance
- **Cloud Sync**: Cross-device pattern sharing
- **Analytics**: Usage insights and optimization

---

## 📞 Support

- **Issues**: [GitHub Issues](https://github.com/UIOrbit/uiorbit-vscode/issues)
- **Discussions**: [GitHub Discussions](https://github.com/UIOrbit/uiorbit-vscode/discussions)
- **Documentation**: [Wiki](https://github.com/UIOrbit/uiorbit-vscode/wiki)

Happy coding! 🚀
