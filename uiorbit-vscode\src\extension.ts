import * as vscode from "vscode";

import UIOrbitViewProvider from './uiorbit-view-provider';

const menuCommands = ["addStyling", "makeResponsive", "addAccessibility", "optimizeUI", "explainDesign", "generateVariants", "generateComponent", "trendingPatterns", "designSystem", "customUIPrompt"];

export async function activate(context: vscode.ExtensionContext) {
	let customUIPromptPrefix: string = context.globalState.get("uiorbit-custom-prompt") || '';

	const provider = new UIOrbitViewProvider(context);

	const view = vscode.window.registerWebviewViewProvider(
		"uiorbit.view",
		provider,
		{
			webviewOptions: {
				retainContextWhenHidden: true,
			},
		}
	);

	const freeText = vscode.commands.registerCommand("uiorbit.freeText", async () => {
		const value = await vscode.window.showInputBox({
			prompt: "Ask anything about UI/UX...",
		});

		if (value) {
			provider?.sendApiRequest(value, { command: "freeText" });
		}
	});

	const resetThread = vscode.commands.registerCommand("uiorbit.clearConversation", async () => {
		provider?.sendMessage({ type: 'clearConversation' }, true);
	});

	const exportConversation = vscode.commands.registerCommand("uiorbit.exportConversation", async () => {
		provider?.sendMessage({ type: 'exportConversation' }, true);
	});

	const clearSession = vscode.commands.registerCommand("uiorbit.clearSession", () => {
		context.globalState.update("uiorbit-session-token", null);
		context.globalState.update("uiorbit-api-key", null);
		provider?.clearSession();
	});

	const configChanged = vscode.workspace.onDidChangeConfiguration(e => {
		if (e.affectsConfiguration('uiorbit.response.showNotification')) {
			provider.subscribeToResponse = vscode.workspace.getConfiguration("uiorbit").get("response.showNotification") || false;
		}

		if (e.affectsConfiguration('uiorbit.response.autoScroll')) {
			provider.autoScroll = !!vscode.workspace.getConfiguration("uiorbit").get("response.autoScroll");
		}

		if (e.affectsConfiguration('uiorbit.framework')) {
			provider.framework = vscode.workspace.getConfiguration("uiorbit").get("framework");
		}

		if (e.affectsConfiguration('uiorbit.stylingFramework')) {
			provider.stylingFramework = vscode.workspace.getConfiguration("uiorbit").get("stylingFramework");
		}

		if (e.affectsConfiguration('uiorbit.designSystem')) {
			provider.designSystem = vscode.workspace.getConfiguration("uiorbit").get("designSystem");
		}

		if (e.affectsConfiguration('uiorbit.method')) {
			provider.setMethod();
		}

		if (e.affectsConfiguration('uiorbit.model')) {
			provider.model = vscode.workspace.getConfiguration("uiorbit").get("model");
		}

		if (e.affectsConfiguration('uiorbit.apiBaseUrl')
			|| e.affectsConfiguration('uiorbit.model')
			|| e.affectsConfiguration('uiorbit.organization')
			|| e.affectsConfiguration('uiorbit.maxTokens')
			|| e.affectsConfiguration('uiorbit.temperature')) {
			provider.prepareConversation(true);
		}

		if (e.affectsConfiguration('uiorbit.promptPrefix') || e.affectsConfiguration('uiorbit.generateComponent-enabled') || e.affectsConfiguration('uiorbit.model') || e.affectsConfiguration('uiorbit.method')) {
			setContext();
		}
	});

	const customUIPromptCommand = vscode.commands.registerCommand("uiorbit.customUIPrompt", async () => {
		const editor = vscode.window.activeTextEditor;

		if (!editor) {
			return;
		}

		const selection = editor.document.getText(editor.selection);
		let dismissed = false;
		if (selection) {
			await vscode.window
				.showInputBox({
					title: "Custom UI Prompt",
					prompt: "Enter your custom UI/UX prompt. e.g., 'Make this component more accessible'",
					ignoreFocusOut: true,
					placeHolder: "Describe what you want to do with this UI component...",
					value: customUIPromptPrefix
				})
				.then((value) => {
					if (!value) {
						dismissed = true;
						return;
					}

					customUIPromptPrefix = value.trim() || '';
					context.globalState.update("uiorbit-custom-prompt", customUIPromptPrefix);
				});

			if (!dismissed && customUIPromptPrefix?.length > 0) {
				provider?.sendApiRequest(customUIPromptPrefix, { command: "customUIPrompt", code: selection });
			}
		}
	});

	const generateComponentCommand = vscode.commands.registerCommand(`uiorbit.generateComponent`, () => {
		const editor = vscode.window.activeTextEditor;

		if (!editor) {
			return;
		}

		const selection = editor.document.getText(editor.selection);
		if (selection) {
			provider?.sendApiRequest(selection, { command: "generateComponent", language: editor.document.languageId });
		}
	});

	// Skip customUIPrompt and generateComponent - as they were registered earlier
	const registeredCommands = menuCommands.filter(command => command !== "customUIPrompt" && command !== "generateComponent").map((command) => vscode.commands.registerCommand(`uiorbit.${command}`, () => {
		const prompt = vscode.workspace.getConfiguration("uiorbit").get<string>(`promptPrefix.${command}`);
		const editor = vscode.window.activeTextEditor;

		if (!editor) {
			return;
		}

		const selection = editor.document.getText(editor.selection);
		if (selection && prompt) {
			provider?.sendApiRequest(prompt, { command, code: selection, language: editor.document.languageId });
		}
	}));

	// Enhanced UIOrbit commands for component libraries
	const installShadcn = vscode.commands.registerCommand("uiorbit.installShadcn", async () => {
		await provider?.installComponentLibrary('shadcn');
	});

	const installMUI = vscode.commands.registerCommand("uiorbit.installMUI", async () => {
		await provider?.installComponentLibrary('mui');
	});

	const installAntd = vscode.commands.registerCommand("uiorbit.installAntd", async () => {
		await provider?.installComponentLibrary('antd');
	});

	const installChakra = vscode.commands.registerCommand("uiorbit.installChakra", async () => {
		await provider?.installComponentLibrary('chakra');
	});

	const generateFromTemplate = vscode.commands.registerCommand("uiorbit.generateFromTemplate", async () => {
		await provider?.generateFromTemplate();
	});

	const showEnterpriseDashboard = vscode.commands.registerCommand("uiorbit.showEnterpriseDashboard", () => {
		provider?.showEnterpriseDashboard();
	});

	const startCollaboration = vscode.commands.registerCommand("uiorbit.startCollaboration", async () => {
		await provider?.startCollaboration();
	});

	const showCollaborationPanel = vscode.commands.registerCommand("uiorbit.showCollaborationPanel", () => {
		provider?.showCollaborationPanel();
	});

	context.subscriptions.push(view, freeText, resetThread, exportConversation, clearSession, configChanged, customUIPromptCommand, generateComponentCommand, ...registeredCommands, installShadcn, installMUI, installAntd, installChakra, generateFromTemplate, showEnterpriseDashboard, startCollaboration, showCollaborationPanel);

	const setContext = () => {
		menuCommands.forEach(command => {
			if (command === "generateComponent") {
				let generateComponentEnabled = !!vscode.workspace.getConfiguration("uiorbit").get<boolean>("generateComponent-enabled");
				vscode.commands.executeCommand('setContext', "generateComponent-enabled", generateComponentEnabled);
			} else {
				const enabled = !!vscode.workspace.getConfiguration("uiorbit.promptPrefix").get<boolean>(`${command}-enabled`);
				vscode.commands.executeCommand('setContext', `${command}-enabled`, enabled);
			}
		});
	};

	setContext();
}

export function deactivate() {
	// Clean up resources when extension is deactivated
	// The provider will be disposed automatically by VS Code
}
