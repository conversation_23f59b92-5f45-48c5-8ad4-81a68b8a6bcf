import * as vscode from "vscode";

import { CleanUIOrbitViewProvider } from './clean-view-provider';
import { VectorDatabase } from './vector-database';
import { FileWatcher } from './file-watcher';

const menuCommands = ["addStyling", "makeResponsive", "addAccessibility", "optimizeUI", "explainDesign", "generateVariants", "generateComponent", "trendingPatterns", "designSystem", "customUIPrompt"];

export async function activate(context: vscode.ExtensionContext) {
	console.log('UIOrbit extension is activating...');

	const provider = new CleanUIOrbitViewProvider(context);

	const view = vscode.window.registerWebviewViewProvider(
		"uiorbit.view",
		provider,
		{
			webviewOptions: {
				retainContextWhenHidden: true,
			},
		}
	);

	const freeText = vscode.commands.registerCommand("uiorbit.freeText", async () => {
		const value = await vscode.window.showInputBox({
			prompt: "Describe the UI component you want to generate...",
			placeHolder: "e.g., responsive navigation bar with dark mode toggle"
		});

		if (value) {
			// The clean provider will handle this through its webview
			vscode.commands.executeCommand('uiorbit.view.focus');
		}
	});

	const clearConversation = vscode.commands.registerCommand("uiorbit.clearConversation", async () => {
		// Clear conversation through the provider
		vscode.commands.executeCommand('uiorbit.view.focus');
	});

	// Simple configuration monitoring for API key changes
	const configChanged = vscode.workspace.onDidChangeConfiguration(e => {
		if (e.affectsConfiguration('uiorbit.apiKey') || e.affectsConfiguration('uiorbit.enableLocalIndexing')) {
			// Configuration changed - provider will handle this automatically
			console.log('UIOrbit configuration updated');
		}
	});

	const customUIPromptCommand = vscode.commands.registerCommand("uiorbit.customUIPrompt", async () => {
		const editor = vscode.window.activeTextEditor;

		if (!editor) {
			return;
		}

		const selection = editor.document.getText(editor.selection);
		let dismissed = false;
		if (selection) {
			await vscode.window
				.showInputBox({
					title: "Custom UI Prompt",
					prompt: "Enter your custom UI/UX prompt. e.g., 'Make this component more accessible'",
					ignoreFocusOut: true,
					placeHolder: "Describe what you want to do with this UI component...",
					value: customUIPromptPrefix
				})
				.then((value) => {
					if (!value) {
						dismissed = true;
						return;
					}

					customUIPromptPrefix = value.trim() || '';
					context.globalState.update("uiorbit-custom-prompt", customUIPromptPrefix);
				});

			if (!dismissed && customUIPromptPrefix?.length > 0) {
				provider?.sendApiRequest(customUIPromptPrefix, { command: "customUIPrompt", code: selection });
			}
		}
	});

	const generateComponentCommand = vscode.commands.registerCommand(`uiorbit.generateComponent`, () => {
		const editor = vscode.window.activeTextEditor;

		if (!editor) {
			return;
		}

		const selection = editor.document.getText(editor.selection);
		if (selection) {
			provider?.sendApiRequest(selection, { command: "generateComponent", language: editor.document.languageId });
		}
	});

	// Skip customUIPrompt and generateComponent - as they were registered earlier
	const registeredCommands = menuCommands.filter(command => command !== "customUIPrompt" && command !== "generateComponent").map((command) => vscode.commands.registerCommand(`uiorbit.${command}`, () => {
		const prompt = vscode.workspace.getConfiguration("uiorbit").get<string>(`promptPrefix.${command}`);
		const editor = vscode.window.activeTextEditor;

		if (!editor) {
			return;
		}

		const selection = editor.document.getText(editor.selection);
		if (selection && prompt) {
			provider?.sendApiRequest(prompt, { command, code: selection, language: editor.document.languageId });
		}
	}));

	// Enhanced UIOrbit commands for component libraries
	const installShadcn = vscode.commands.registerCommand("uiorbit.installShadcn", async () => {
		await provider?.installComponentLibrary('shadcn');
	});

	const installMUI = vscode.commands.registerCommand("uiorbit.installMUI", async () => {
		await provider?.installComponentLibrary('mui');
	});

	const installAntd = vscode.commands.registerCommand("uiorbit.installAntd", async () => {
		await provider?.installComponentLibrary('antd');
	});

	const installChakra = vscode.commands.registerCommand("uiorbit.installChakra", async () => {
		await provider?.installComponentLibrary('chakra');
	});

	const generateFromTemplate = vscode.commands.registerCommand("uiorbit.generateFromTemplate", async () => {
		await provider?.generateFromTemplate();
	});

	const showEnterpriseDashboard = vscode.commands.registerCommand("uiorbit.showEnterpriseDashboard", () => {
		provider?.showEnterpriseDashboard();
	});

	const startCollaboration = vscode.commands.registerCommand("uiorbit.startCollaboration", async () => {
		await provider?.startCollaboration();
	});

	const showCollaborationPanel = vscode.commands.registerCommand("uiorbit.showCollaborationPanel", () => {
		provider?.showCollaborationPanel();
	});

	context.subscriptions.push(view, freeText, resetThread, exportConversation, clearSession, configChanged, customUIPromptCommand, generateComponentCommand, ...registeredCommands, installShadcn, installMUI, installAntd, installChakra, generateFromTemplate, showEnterpriseDashboard, startCollaboration, showCollaborationPanel);

	const setContext = () => {
		menuCommands.forEach(command => {
			if (command === "generateComponent") {
				let generateComponentEnabled = !!vscode.workspace.getConfiguration("uiorbit").get<boolean>("generateComponent-enabled");
				vscode.commands.executeCommand('setContext', "generateComponent-enabled", generateComponentEnabled);
			} else {
				const enabled = !!vscode.workspace.getConfiguration("uiorbit.promptPrefix").get<boolean>(`${command}-enabled`);
				vscode.commands.executeCommand('setContext', `${command}-enabled`, enabled);
			}
		});
	};

	setContext();
}

export function deactivate() {
	// Clean up resources when extension is deactivated
	// The provider will be disposed automatically by VS Code
}
