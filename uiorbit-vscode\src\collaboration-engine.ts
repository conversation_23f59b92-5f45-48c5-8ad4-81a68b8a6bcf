/**
 * UIOrbit Real-time Collaboration Engine
 * Live collaboration features for team development
 */

import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';

export interface CollaborationSession {
    id: string;
    name: string;
    participants: Participant[];
    sharedFiles: string[];
    createdAt: Date;
    lastActivity: Date;
}

export interface Participant {
    id: string;
    name: string;
    avatar?: string;
    cursor?: {
        file: string;
        line: number;
        column: number;
    };
    selection?: {
        file: string;
        start: { line: number; column: number };
        end: { line: number; column: number };
    };
    isActive: boolean;
}

export interface CollaborationEvent {
    type: 'cursor' | 'selection' | 'edit' | 'comment' | 'join' | 'leave';
    participant: string;
    data: any;
    timestamp: Date;
}

export interface SharedComment {
    id: string;
    author: string;
    content: string;
    file: string;
    line: number;
    resolved: boolean;
    timestamp: Date;
    replies: SharedComment[];
}

export class CollaborationEngine {
    private currentSession: CollaborationSession | null = null;
    private participants: Map<string, Participant> = new Map();
    private comments: Map<string, SharedComment> = new Map();
    private eventHandlers: Map<string, Function[]> = new Map();
    private workspaceRoot: string;

    constructor(workspaceRoot: string) {
        this.workspaceRoot = workspaceRoot;
        this.setupEventListeners();
    }

    /**
     * Start a new collaboration session
     */
    async startSession(sessionName: string): Promise<CollaborationSession> {
        const session: CollaborationSession = {
            id: this.generateSessionId(),
            name: sessionName,
            participants: [],
            sharedFiles: [],
            createdAt: new Date(),
            lastActivity: new Date()
        };

        this.currentSession = session;
        
        // Show session info
        vscode.window.showInformationMessage(
            `🚀 Collaboration session "${sessionName}" started!`,
            'Share Link',
            'Invite Team'
        ).then(action => {
            if (action === 'Share Link') {
                this.shareSessionLink();
            } else if (action === 'Invite Team') {
                this.inviteTeamMembers();
            }
        });

        return session;
    }

    /**
     * Join an existing collaboration session
     */
    async joinSession(sessionId: string, participantName: string): Promise<boolean> {
        try {
            // In a real implementation, this would connect to a server
            const participant: Participant = {
                id: this.generateParticipantId(),
                name: participantName,
                isActive: true
            };

            this.participants.set(participant.id, participant);
            
            if (this.currentSession) {
                this.currentSession.participants.push(participant);
                this.broadcastEvent({
                    type: 'join',
                    participant: participant.id,
                    data: participant,
                    timestamp: new Date()
                });
            }

            vscode.window.showInformationMessage(`✅ Joined collaboration session as ${participantName}`);
            return true;
        } catch (error) {
            vscode.window.showErrorMessage(`❌ Failed to join session: ${error}`);
            return false;
        }
    }

    /**
     * Share current cursor position with team
     */
    shareCursor(file: string, line: number, column: number): void {
        if (!this.currentSession) return;

        const event: CollaborationEvent = {
            type: 'cursor',
            participant: 'current-user',
            data: { file, line, column },
            timestamp: new Date()
        };

        this.broadcastEvent(event);
        this.updateParticipantCursor('current-user', file, line, column);
    }

    /**
     * Share current selection with team
     */
    shareSelection(file: string, start: {line: number, column: number}, end: {line: number, column: number}): void {
        if (!this.currentSession) return;

        const event: CollaborationEvent = {
            type: 'selection',
            participant: 'current-user',
            data: { file, start, end },
            timestamp: new Date()
        };

        this.broadcastEvent(event);
        this.updateParticipantSelection('current-user', file, start, end);
    }

    /**
     * Add a comment to a specific line
     */
    async addComment(file: string, line: number, content: string): Promise<SharedComment> {
        const comment: SharedComment = {
            id: this.generateCommentId(),
            author: 'current-user',
            content,
            file,
            line,
            resolved: false,
            timestamp: new Date(),
            replies: []
        };

        this.comments.set(comment.id, comment);
        
        // Show comment in UI
        this.showCommentDecoration(comment);
        
        // Broadcast to team
        this.broadcastEvent({
            type: 'comment',
            participant: 'current-user',
            data: comment,
            timestamp: new Date()
        });

        return comment;
    }

    /**
     * Get all participants in current session
     */
    getParticipants(): Participant[] {
        return this.currentSession?.participants || [];
    }

    /**
     * Get all comments for a file
     */
    getCommentsForFile(file: string): SharedComment[] {
        return Array.from(this.comments.values()).filter(comment => comment.file === file);
    }

    /**
     * Show collaboration panel
     */
    showCollaborationPanel(): void {
        const panel = vscode.window.createWebviewPanel(
            'uiorbitCollaboration',
            '👥 UIOrbit Collaboration',
            vscode.ViewColumn.Beside,
            {
                enableScripts: true,
                retainContextWhenHidden: true
            }
        );

        panel.webview.html = this.getCollaborationPanelHtml();
        
        // Handle messages from webview
        panel.webview.onDidReceiveMessage(message => {
            switch (message.command) {
                case 'startSession':
                    this.startSession(message.sessionName);
                    break;
                case 'joinSession':
                    this.joinSession(message.sessionId, message.participantName);
                    break;
                case 'addComment':
                    this.addComment(message.file, message.line, message.content);
                    break;
            }
        });
    }

    private setupEventListeners(): void {
        // Listen for cursor changes
        vscode.window.onDidChangeTextEditorSelection(event => {
            if (event.textEditor.document.uri.scheme === 'file') {
                const file = event.textEditor.document.fileName;
                const selection = event.selections[0];
                
                if (selection.isEmpty) {
                    // Cursor position
                    this.shareCursor(file, selection.start.line, selection.start.character);
                } else {
                    // Selection
                    this.shareSelection(
                        file,
                        { line: selection.start.line, column: selection.start.character },
                        { line: selection.end.line, column: selection.end.character }
                    );
                }
            }
        });

        // Listen for file changes
        vscode.workspace.onDidChangeTextDocument(event => {
            if (this.currentSession && event.document.uri.scheme === 'file') {
                this.broadcastEvent({
                    type: 'edit',
                    participant: 'current-user',
                    data: {
                        file: event.document.fileName,
                        changes: event.contentChanges
                    },
                    timestamp: new Date()
                });
            }
        });
    }

    private broadcastEvent(event: CollaborationEvent): void {
        // In a real implementation, this would send to a collaboration server
        console.log('Broadcasting event:', event);
        
        // Trigger local event handlers
        const handlers = this.eventHandlers.get(event.type) || [];
        handlers.forEach(handler => handler(event));
    }

    private updateParticipantCursor(participantId: string, file: string, line: number, column: number): void {
        const participant = this.participants.get(participantId);
        if (participant) {
            participant.cursor = { file, line, column };
            this.showCursorDecoration(participant);
        }
    }

    private updateParticipantSelection(participantId: string, file: string, start: {line: number, column: number}, end: {line: number, column: number}): void {
        const participant = this.participants.get(participantId);
        if (participant) {
            participant.selection = { file, start, end };
            this.showSelectionDecoration(participant);
        }
    }

    private showCursorDecoration(participant: Participant): void {
        // Show other participants' cursors in the editor
        if (participant.cursor) {
            const decorationType = vscode.window.createTextEditorDecorationType({
                backgroundColor: this.getParticipantColor(participant.id),
                border: '1px solid',
                borderColor: this.getParticipantColor(participant.id)
            });

            // Apply decoration to active editor if it matches the file
            const activeEditor = vscode.window.activeTextEditor;
            if (activeEditor && activeEditor.document.fileName === participant.cursor.file) {
                const range = new vscode.Range(
                    participant.cursor.line,
                    participant.cursor.column,
                    participant.cursor.line,
                    participant.cursor.column + 1
                );
                activeEditor.setDecorations(decorationType, [range]);
            }
        }
    }

    private showSelectionDecoration(participant: Participant): void {
        // Show other participants' selections in the editor
        if (participant.selection) {
            const decorationType = vscode.window.createTextEditorDecorationType({
                backgroundColor: this.getParticipantColor(participant.id) + '40', // 25% opacity
                border: '1px solid',
                borderColor: this.getParticipantColor(participant.id)
            });

            const activeEditor = vscode.window.activeTextEditor;
            if (activeEditor && activeEditor.document.fileName === participant.selection.file) {
                const range = new vscode.Range(
                    participant.selection.start.line,
                    participant.selection.start.column,
                    participant.selection.end.line,
                    participant.selection.end.column
                );
                activeEditor.setDecorations(decorationType, [range]);
            }
        }
    }

    private showCommentDecoration(comment: SharedComment): void {
        const decorationType = vscode.window.createTextEditorDecorationType({
            backgroundColor: '#ffeb3b40',
            border: '1px solid #ffeb3b',
            after: {
                contentText: ` 💬 ${comment.author}`,
                color: '#666',
                fontStyle: 'italic'
            }
        });

        const activeEditor = vscode.window.activeTextEditor;
        if (activeEditor && activeEditor.document.fileName === comment.file) {
            const range = new vscode.Range(comment.line, 0, comment.line, 0);
            activeEditor.setDecorations(decorationType, [range]);
        }
    }

    private getParticipantColor(participantId: string): string {
        const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7', '#dda0dd'];
        const index = participantId.charCodeAt(0) % colors.length;
        return colors[index];
    }

    private getCollaborationPanelHtml(): string {
        return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UIOrbit Collaboration</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .panel {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .participant {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            margin-bottom: 8px;
        }
        .participant-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #4ecdc4;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }
        input {
            width: 100%;
            padding: 10px;
            border: none;
            border-radius: 8px;
            margin-bottom: 10px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
        }
    </style>
</head>
<body>
    <div class="panel">
        <h2>🚀 Start Collaboration</h2>
        <input type="text" id="sessionName" placeholder="Session name">
        <button onclick="startSession()">Start Session</button>
    </div>

    <div class="panel">
        <h2>👥 Join Session</h2>
        <input type="text" id="sessionId" placeholder="Session ID">
        <input type="text" id="participantName" placeholder="Your name">
        <button onclick="joinSession()">Join Session</button>
    </div>

    <div class="panel">
        <h2>👥 Participants</h2>
        <div id="participants">
            <div class="participant">
                <div class="participant-avatar">You</div>
                <div>
                    <div>You (Host)</div>
                    <div style="font-size: 12px; opacity: 0.8;">Active</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const vscode = acquireVsCodeApi();

        function startSession() {
            const sessionName = document.getElementById('sessionName').value;
            if (sessionName) {
                vscode.postMessage({
                    command: 'startSession',
                    sessionName: sessionName
                });
            }
        }

        function joinSession() {
            const sessionId = document.getElementById('sessionId').value;
            const participantName = document.getElementById('participantName').value;
            if (sessionId && participantName) {
                vscode.postMessage({
                    command: 'joinSession',
                    sessionId: sessionId,
                    participantName: participantName
                });
            }
        }
    </script>
</body>
</html>`;
    }

    private generateSessionId(): string {
        return Math.random().toString(36).substring(2, 15);
    }

    private generateParticipantId(): string {
        return Math.random().toString(36).substring(2, 15);
    }

    private generateCommentId(): string {
        return Math.random().toString(36).substring(2, 15);
    }

    private async shareSessionLink(): Promise<void> {
        const sessionLink = `uiorbit://join/${this.currentSession?.id}`;
        await vscode.env.clipboard.writeText(sessionLink);
        vscode.window.showInformationMessage('📋 Session link copied to clipboard!');
    }

    private async inviteTeamMembers(): Promise<void> {
        const emails = await vscode.window.showInputBox({
            prompt: 'Enter email addresses (comma-separated)',
            placeHolder: '<EMAIL>, <EMAIL>'
        });

        if (emails) {
            // In a real implementation, this would send invitations
            vscode.window.showInformationMessage(`📧 Invitations sent to: ${emails}`);
        }
    }

    /**
     * Subscribe to collaboration events
     */
    on(eventType: string, handler: Function): void {
        if (!this.eventHandlers.has(eventType)) {
            this.eventHandlers.set(eventType, []);
        }
        this.eventHandlers.get(eventType)!.push(handler);
    }

    /**
     * End current collaboration session
     */
    endSession(): void {
        if (this.currentSession) {
            this.broadcastEvent({
                type: 'leave',
                participant: 'current-user',
                data: {},
                timestamp: new Date()
            });
            
            this.currentSession = null;
            this.participants.clear();
            this.comments.clear();
            
            vscode.window.showInformationMessage('👋 Collaboration session ended');
        }
    }
}
