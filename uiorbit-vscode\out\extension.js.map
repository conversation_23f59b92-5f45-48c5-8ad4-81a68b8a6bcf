{"version": 3, "sources": ["../src/extension.ts", "../src/clean-view-provider.ts", "../src/vector-database.ts", "../src/file-watcher.ts", "../src/ast-analyzer.ts"], "sourcesContent": ["import * as vscode from \"vscode\";\r\n\r\nimport { CleanUIOrbitViewProvider } from './clean-view-provider';\r\nimport { VectorDatabase } from './vector-database';\r\nimport { FileWatcher } from './file-watcher';\r\n\r\nconst menuCommands = [\"addStyling\", \"makeResponsive\", \"addAccessibility\", \"optimizeUI\", \"explainDesign\", \"generateVariants\", \"generateComponent\", \"trendingPatterns\", \"designSystem\", \"customUIPrompt\"];\r\n\r\nexport async function activate(context: vscode.ExtensionContext) {\r\n\tconsole.log('UIOrbit extension is activating...');\r\n\r\n\tconst provider = new CleanUIOrbitViewProvider(context);\r\n\r\n\tconst view = vscode.window.registerWebviewViewProvider(\r\n\t\t\"uiorbit.view\",\r\n\t\tprovider,\r\n\t\t{\r\n\t\t\twebviewOptions: {\r\n\t\t\t\tretainContextWhenHidden: true,\r\n\t\t\t},\r\n\t\t}\r\n\t);\r\n\r\n\tconst freeText = vscode.commands.registerCommand(\"uiorbit.freeText\", async () => {\r\n\t\tconst value = await vscode.window.showInputBox({\r\n\t\t\tprompt: \"Describe the UI component you want to generate...\",\r\n\t\t\tplaceHolder: \"e.g., responsive navigation bar with dark mode toggle\"\r\n\t\t});\r\n\r\n\t\tif (value) {\r\n\t\t\t// The clean provider will handle this through its webview\r\n\t\t\tvscode.commands.executeCommand('uiorbit.view.focus');\r\n\t\t}\r\n\t});\r\n\r\n\tconst clearConversation = vscode.commands.registerCommand(\"uiorbit.clearConversation\", async () => {\r\n\t\t// Clear conversation through the provider\r\n\t\tvscode.commands.executeCommand('uiorbit.view.focus');\r\n\t});\r\n\r\n\t// Simple configuration monitoring for API key changes\r\n\tconst configChanged = vscode.workspace.onDidChangeConfiguration(e => {\r\n\t\tif (e.affectsConfiguration('uiorbit.apiKey') || e.affectsConfiguration('uiorbit.enableLocalIndexing')) {\r\n\t\t\t// Configuration changed - provider will handle this automatically\r\n\t\t\tconsole.log('UIOrbit configuration updated');\r\n\t\t}\r\n\t});\r\n\r\n\tconst customUIPromptCommand = vscode.commands.registerCommand(\"uiorbit.customUIPrompt\", async () => {\r\n\t\tconst editor = vscode.window.activeTextEditor;\r\n\r\n\t\tif (!editor) {\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tconst selection = editor.document.getText(editor.selection);\r\n\t\tlet dismissed = false;\r\n\t\tif (selection) {\r\n\t\t\tawait vscode.window\r\n\t\t\t\t.showInputBox({\r\n\t\t\t\t\ttitle: \"Custom UI Prompt\",\r\n\t\t\t\t\tprompt: \"Enter your custom UI/UX prompt. e.g., 'Make this component more accessible'\",\r\n\t\t\t\t\tignoreFocusOut: true,\r\n\t\t\t\t\tplaceHolder: \"Describe what you want to do with this UI component...\",\r\n\t\t\t\t\tvalue: customUIPromptPrefix\r\n\t\t\t\t})\r\n\t\t\t\t.then((value) => {\r\n\t\t\t\t\tif (!value) {\r\n\t\t\t\t\t\tdismissed = true;\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tcustomUIPromptPrefix = value.trim() || '';\r\n\t\t\t\t\tcontext.globalState.update(\"uiorbit-custom-prompt\", customUIPromptPrefix);\r\n\t\t\t\t});\r\n\r\n\t\t\tif (!dismissed && customUIPromptPrefix?.length > 0) {\r\n\t\t\t\tprovider?.sendApiRequest(customUIPromptPrefix, { command: \"customUIPrompt\", code: selection });\r\n\t\t\t}\r\n\t\t}\r\n\t});\r\n\r\n\tconst generateComponentCommand = vscode.commands.registerCommand(`uiorbit.generateComponent`, () => {\r\n\t\tconst editor = vscode.window.activeTextEditor;\r\n\r\n\t\tif (!editor) {\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tconst selection = editor.document.getText(editor.selection);\r\n\t\tif (selection) {\r\n\t\t\tprovider?.sendApiRequest(selection, { command: \"generateComponent\", language: editor.document.languageId });\r\n\t\t}\r\n\t});\r\n\r\n\t// Skip customUIPrompt and generateComponent - as they were registered earlier\r\n\tconst registeredCommands = menuCommands.filter(command => command !== \"customUIPrompt\" && command !== \"generateComponent\").map((command) => vscode.commands.registerCommand(`uiorbit.${command}`, () => {\r\n\t\tconst prompt = vscode.workspace.getConfiguration(\"uiorbit\").get<string>(`promptPrefix.${command}`);\r\n\t\tconst editor = vscode.window.activeTextEditor;\r\n\r\n\t\tif (!editor) {\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tconst selection = editor.document.getText(editor.selection);\r\n\t\tif (selection && prompt) {\r\n\t\t\tprovider?.sendApiRequest(prompt, { command, code: selection, language: editor.document.languageId });\r\n\t\t}\r\n\t}));\r\n\r\n\t// Enhanced UIOrbit commands for component libraries\r\n\tconst installShadcn = vscode.commands.registerCommand(\"uiorbit.installShadcn\", async () => {\r\n\t\tawait provider?.installComponentLibrary('shadcn');\r\n\t});\r\n\r\n\tconst installMUI = vscode.commands.registerCommand(\"uiorbit.installMUI\", async () => {\r\n\t\tawait provider?.installComponentLibrary('mui');\r\n\t});\r\n\r\n\tconst installAntd = vscode.commands.registerCommand(\"uiorbit.installAntd\", async () => {\r\n\t\tawait provider?.installComponentLibrary('antd');\r\n\t});\r\n\r\n\tconst installChakra = vscode.commands.registerCommand(\"uiorbit.installChakra\", async () => {\r\n\t\tawait provider?.installComponentLibrary('chakra');\r\n\t});\r\n\r\n\tconst generateFromTemplate = vscode.commands.registerCommand(\"uiorbit.generateFromTemplate\", async () => {\r\n\t\tawait provider?.generateFromTemplate();\r\n\t});\r\n\r\n\tconst showEnterpriseDashboard = vscode.commands.registerCommand(\"uiorbit.showEnterpriseDashboard\", () => {\r\n\t\tprovider?.showEnterpriseDashboard();\r\n\t});\r\n\r\n\tconst startCollaboration = vscode.commands.registerCommand(\"uiorbit.startCollaboration\", async () => {\r\n\t\tawait provider?.startCollaboration();\r\n\t});\r\n\r\n\tconst showCollaborationPanel = vscode.commands.registerCommand(\"uiorbit.showCollaborationPanel\", () => {\r\n\t\tprovider?.showCollaborationPanel();\r\n\t});\r\n\r\n\tcontext.subscriptions.push(view, freeText, resetThread, exportConversation, clearSession, configChanged, customUIPromptCommand, generateComponentCommand, ...registeredCommands, installShadcn, installMUI, installAntd, installChakra, generateFromTemplate, showEnterpriseDashboard, startCollaboration, showCollaborationPanel);\r\n\r\n\tconst setContext = () => {\r\n\t\tmenuCommands.forEach(command => {\r\n\t\t\tif (command === \"generateComponent\") {\r\n\t\t\t\tlet generateComponentEnabled = !!vscode.workspace.getConfiguration(\"uiorbit\").get<boolean>(\"generateComponent-enabled\");\r\n\t\t\t\tvscode.commands.executeCommand('setContext', \"generateComponent-enabled\", generateComponentEnabled);\r\n\t\t\t} else {\r\n\t\t\t\tconst enabled = !!vscode.workspace.getConfiguration(\"uiorbit.promptPrefix\").get<boolean>(`${command}-enabled`);\r\n\t\t\t\tvscode.commands.executeCommand('setContext', `${command}-enabled`, enabled);\r\n\t\t\t}\r\n\t\t});\r\n\t};\r\n\r\n\tsetContext();\r\n}\r\n\r\nexport function deactivate() {\r\n\t// Clean up resources when extension is deactivated\r\n\t// The provider will be disposed automatically by VS Code\r\n}\r\n", "/**\n * UIOrbit - Clean Augment Code-style View Provider\n * Privacy-first UI/UX AI assistant with local codebase intelligence\n */\n\nimport * as vscode from 'vscode';\nimport { VectorDatabase } from './vector-database';\nimport { FileWatcher } from './file-watcher';\nimport { ASTAnalyzer } from './ast-analyzer';\n\nexport interface UIOrbitMessage {\n    type: 'request' | 'response' | 'error' | 'status';\n    content: string;\n    timestamp: Date;\n    context?: any;\n}\n\nexport class CleanUIOrbitViewProvider implements vscode.WebviewViewProvider {\n    private webView?: vscode.WebviewView;\n    private vectorDb: VectorDatabase;\n    private fileWatcher: FileWatcher;\n    private astAnalyzer: ASTAnalyzer;\n    private messages: UIOrbitMessage[] = [];\n    private isProcessing: boolean = false;\n\n    constructor(private context: vscode.ExtensionContext) {\n        this.vectorDb = new VectorDatabase(context);\n        this.fileWatcher = new FileWatcher(context, this.vectorDb);\n        this.astAnalyzer = new ASTAnalyzer();\n    }\n\n    public resolveWebviewView(\n        webviewView: vscode.WebviewView,\n        _context: vscode.WebviewViewResolveContext,\n        _token: vscode.CancellationToken\n    ): void {\n        this.webView = webviewView;\n\n        webviewView.webview.options = {\n            enableScripts: true,\n            localResourceRoots: [this.context.extensionUri]\n        };\n\n        webviewView.webview.html = this.getWebviewContent();\n\n        // Handle messages from webview\n        webviewView.webview.onDidReceiveMessage(async (message) => {\n            await this.handleWebviewMessage(message);\n        });\n\n        // Start file watching for real-time codebase intelligence\n        this.initializeServices();\n    }\n\n    private async initializeServices(): Promise<void> {\n        try {\n            // Initialize file watcher for real-time indexing\n            await this.fileWatcher.start();\n            \n            this.addMessage({\n                type: 'status',\n                content: '🚀 UIOrbit initialized - Local codebase intelligence active',\n                timestamp: new Date()\n            });\n        } catch (error) {\n            this.addMessage({\n                type: 'error',\n                content: `Failed to initialize UIOrbit: ${error}`,\n                timestamp: new Date()\n            });\n        }\n    }\n\n    private async handleWebviewMessage(message: any): Promise<void> {\n        switch (message.type) {\n            case 'ui-request':\n                await this.handleUIRequest(message.content);\n                break;\n            case 'get-context':\n                await this.sendProjectContext();\n                break;\n            case 'clear-conversation':\n                this.clearConversation();\n                break;\n            default:\n                console.log('Unknown message type:', message.type);\n        }\n    }\n\n    private async handleUIRequest(prompt: string): Promise<void> {\n        if (this.isProcessing) {\n            this.addMessage({\n                type: 'error',\n                content: 'Please wait for the current request to complete.',\n                timestamp: new Date()\n            });\n            return;\n        }\n\n        this.isProcessing = true;\n        \n        this.addMessage({\n            type: 'request',\n            content: prompt,\n            timestamp: new Date()\n        });\n\n        try {\n            // Get relevant context from local codebase\n            const context = await this.getRelevantContext(prompt);\n            \n            // Generate UI response (placeholder for now)\n            const response = await this.generateUIResponse(prompt, context);\n            \n            this.addMessage({\n                type: 'response',\n                content: response,\n                timestamp: new Date(),\n                context: context\n            });\n\n        } catch (error) {\n            this.addMessage({\n                type: 'error',\n                content: `Error generating UI: ${error}`,\n                timestamp: new Date()\n            });\n        } finally {\n            this.isProcessing = false;\n        }\n    }\n\n    private async getRelevantContext(prompt: string): Promise<any> {\n        // Use vector database to find relevant context\n        const searchResults = await this.vectorDb.semanticSearch(prompt, 5);\n        \n        // Get current file context\n        const activeEditor = vscode.window.activeTextEditor;\n        const currentFileContext = activeEditor \n            ? this.vectorDb.getProjectContext(activeEditor.document.uri.fsPath)\n            : null;\n\n        // Get project analysis\n        const projectAnalysis = await this.astAnalyzer.analyzeProject();\n\n        return {\n            searchResults,\n            currentFile: currentFileContext,\n            project: {\n                framework: projectAnalysis.framework,\n                stylingFramework: projectAnalysis.stylingFramework,\n                hasAccessibility: projectAnalysis.accessibility,\n                isResponsive: projectAnalysis.responsive\n            }\n        };\n    }\n\n    private async generateUIResponse(prompt: string, context: any): Promise<string> {\n        // Placeholder for AI generation - will be connected to backend API\n        const apiKey = vscode.workspace.getConfiguration('uiorbit').get<string>('apiKey');\n        \n        if (!apiKey) {\n            return `\n## ⚠️ API Key Required\n\nTo use UIOrbit, please set your API key in VS Code settings:\n\n1. Open Settings (Ctrl+,)\n2. Search for \"UIOrbit API Key\"\n3. Enter your API key from [UIOrbit Dashboard](https://uiorbit.dev/dashboard)\n\n---\n\n**Context-Aware Analysis:**\n- Framework: ${context.project.framework}\n- Styling: ${context.project.stylingFramework}\n- Accessibility: ${context.project.hasAccessibility ? '✅' : '❌'}\n- Responsive: ${context.project.isResponsive ? '✅' : '❌'}\n- Relevant patterns found: ${context.searchResults.length}\n\n**Your request:** ${prompt}\n            `;\n        }\n\n        // TODO: Implement actual API call to UIOrbit backend\n        return `\n## 🎨 Generated UI Component\n\n\\`\\`\\`tsx\ninterface ${this.extractComponentName(prompt)}Props {\n  children?: React.ReactNode;\n  className?: string;\n}\n\nexport const ${this.extractComponentName(prompt)}: React.FC<${this.extractComponentName(prompt)}Props> = ({ \n  children, \n  className = '' \n}) => {\n  return (\n    <div className={\\`modern-component \\${className}\\`}>\n      {children}\n    </div>\n  );\n};\n\\`\\`\\`\n\n**Context Used:**\n- Framework: ${context.project.framework}\n- Styling: ${context.project.stylingFramework}\n- Found ${context.searchResults.length} relevant patterns in your codebase\n\n*This is a placeholder response. Connect your API key to get AI-generated components.*\n        `;\n    }\n\n    private extractComponentName(prompt: string): string {\n        // Simple extraction - can be enhanced\n        const words = prompt.split(' ').filter(word => word.length > 2);\n        return words.length > 0 ? words[0].charAt(0).toUpperCase() + words[0].slice(1) : 'Component';\n    }\n\n    private async sendProjectContext(): Promise<void> {\n        const status = this.fileWatcher.getIndexingStatus();\n        const trendingPatterns = this.vectorDb.getTrendingPatterns();\n        \n        this.webView?.webview.postMessage({\n            type: 'project-context',\n            data: {\n                indexingStatus: status,\n                trendingPatterns: trendingPatterns.slice(0, 5),\n                totalPatterns: trendingPatterns.length\n            }\n        });\n    }\n\n    private addMessage(message: UIOrbitMessage): void {\n        this.messages.push(message);\n        this.updateWebview();\n    }\n\n    private clearConversation(): void {\n        this.messages = [];\n        this.updateWebview();\n    }\n\n    private updateWebview(): void {\n        if (this.webView) {\n            this.webView.webview.postMessage({\n                type: 'update-messages',\n                messages: this.messages\n            });\n        }\n    }\n\n    private getWebviewContent(): string {\n        return `\n<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>UIOrbit</title>\n    <style>\n        body {\n            font-family: var(--vscode-font-family);\n            font-size: var(--vscode-font-size);\n            color: var(--vscode-foreground);\n            background-color: var(--vscode-editor-background);\n            margin: 0;\n            padding: 16px;\n        }\n        \n        .header {\n            display: flex;\n            align-items: center;\n            margin-bottom: 16px;\n            padding-bottom: 12px;\n            border-bottom: 1px solid var(--vscode-panel-border);\n        }\n        \n        .logo {\n            font-size: 18px;\n            font-weight: bold;\n            color: var(--vscode-textLink-foreground);\n        }\n        \n        .status {\n            margin-left: auto;\n            font-size: 12px;\n            color: var(--vscode-descriptionForeground);\n        }\n        \n        .input-container {\n            display: flex;\n            gap: 8px;\n            margin-bottom: 16px;\n        }\n        \n        .input-field {\n            flex: 1;\n            padding: 8px 12px;\n            background: var(--vscode-input-background);\n            border: 1px solid var(--vscode-input-border);\n            color: var(--vscode-input-foreground);\n            border-radius: 4px;\n        }\n        \n        .send-button {\n            padding: 8px 16px;\n            background: var(--vscode-button-background);\n            color: var(--vscode-button-foreground);\n            border: none;\n            border-radius: 4px;\n            cursor: pointer;\n        }\n        \n        .send-button:hover {\n            background: var(--vscode-button-hoverBackground);\n        }\n        \n        .send-button:disabled {\n            opacity: 0.5;\n            cursor: not-allowed;\n        }\n        \n        .messages {\n            max-height: 400px;\n            overflow-y: auto;\n        }\n        \n        .message {\n            margin-bottom: 12px;\n            padding: 8px;\n            border-radius: 4px;\n        }\n        \n        .message.request {\n            background: var(--vscode-textBlockQuote-background);\n            border-left: 3px solid var(--vscode-textLink-foreground);\n        }\n        \n        .message.response {\n            background: var(--vscode-editor-background);\n            border: 1px solid var(--vscode-panel-border);\n        }\n        \n        .message.error {\n            background: var(--vscode-inputValidation-errorBackground);\n            border-left: 3px solid var(--vscode-inputValidation-errorBorder);\n        }\n        \n        .message.status {\n            background: var(--vscode-inputValidation-infoBackground);\n            border-left: 3px solid var(--vscode-inputValidation-infoBorder);\n        }\n        \n        .timestamp {\n            font-size: 11px;\n            color: var(--vscode-descriptionForeground);\n            margin-bottom: 4px;\n        }\n        \n        .clear-button {\n            padding: 4px 8px;\n            background: var(--vscode-button-secondaryBackground);\n            color: var(--vscode-button-secondaryForeground);\n            border: none;\n            border-radius: 3px;\n            cursor: pointer;\n            font-size: 12px;\n        }\n        \n        pre {\n            background: var(--vscode-textCodeBlock-background);\n            padding: 8px;\n            border-radius: 4px;\n            overflow-x: auto;\n            font-size: 13px;\n        }\n    </style>\n</head>\n<body>\n    <div class=\"header\">\n        <div class=\"logo\">🎨 UIOrbit</div>\n        <div class=\"status\" id=\"status\">Ready</div>\n    </div>\n    \n    <div class=\"input-container\">\n        <input \n            type=\"text\" \n            class=\"input-field\" \n            id=\"promptInput\" \n            placeholder=\"Describe the UI component you want to generate...\"\n            onkeypress=\"handleKeyPress(event)\"\n        />\n        <button class=\"send-button\" id=\"sendButton\" onclick=\"sendMessage()\">Generate</button>\n        <button class=\"clear-button\" onclick=\"clearConversation()\">Clear</button>\n    </div>\n    \n    <div class=\"messages\" id=\"messages\"></div>\n    \n    <script>\n        const vscode = acquireVsCodeApi();\n        let isProcessing = false;\n        \n        function handleKeyPress(event) {\n            if (event.key === 'Enter' && !event.shiftKey) {\n                event.preventDefault();\n                sendMessage();\n            }\n        }\n        \n        function sendMessage() {\n            const input = document.getElementById('promptInput');\n            const prompt = input.value.trim();\n            \n            if (!prompt || isProcessing) return;\n            \n            input.value = '';\n            isProcessing = true;\n            updateSendButton();\n            \n            vscode.postMessage({\n                type: 'ui-request',\n                content: prompt\n            });\n        }\n        \n        function clearConversation() {\n            vscode.postMessage({ type: 'clear-conversation' });\n        }\n        \n        function updateSendButton() {\n            const button = document.getElementById('sendButton');\n            button.disabled = isProcessing;\n            button.textContent = isProcessing ? 'Generating...' : 'Generate';\n        }\n        \n        function updateMessages(messages) {\n            const container = document.getElementById('messages');\n            container.innerHTML = '';\n            \n            messages.forEach(message => {\n                const div = document.createElement('div');\n                div.className = \\`message \\${message.type}\\`;\n                \n                const timestamp = document.createElement('div');\n                timestamp.className = 'timestamp';\n                timestamp.textContent = new Date(message.timestamp).toLocaleTimeString();\n                \n                const content = document.createElement('div');\n                content.innerHTML = message.content.replace(/\\\\n/g, '<br>');\n                \n                div.appendChild(timestamp);\n                div.appendChild(content);\n                container.appendChild(div);\n            });\n            \n            container.scrollTop = container.scrollHeight;\n            isProcessing = false;\n            updateSendButton();\n        }\n        \n        // Handle messages from extension\n        window.addEventListener('message', event => {\n            const message = event.data;\n            \n            switch (message.type) {\n                case 'update-messages':\n                    updateMessages(message.messages);\n                    break;\n                case 'project-context':\n                    document.getElementById('status').textContent = \n                        \\`\\${message.data.totalPatterns} patterns indexed\\`;\n                    break;\n            }\n        });\n        \n        // Request initial context\n        vscode.postMessage({ type: 'get-context' });\n    </script>\n</body>\n</html>\n        `;\n    }\n\n    public dispose(): void {\n        this.fileWatcher.dispose();\n    }\n}\n", "/**\n * UIOrbit - Local Vector Database for UI patterns and context storage\n * Augment Code-style local-first vector database with SQLite + vector extensions\n * Privacy-first: All user code processing happens locally\n */\n\nimport * as vscode from 'vscode';\n\nimport * as fs from 'fs';\n\nimport * as path from 'path';\n\nimport Database from 'better-sqlite3';\n\nimport { loadVss } from 'sqlite-vss';\n\nexport interface UIPattern {\n    id: string;\n    name: string;\n    description: string;\n    framework: string;\n    category: 'component' | 'layout' | 'styling' | 'interaction' | 'accessibility' | 'animation';\n    tags: string[];\n    code: string;\n    embedding: Float32Array;\n    usageCount: number;\n    trendScore: number;\n    accessibilityScore: number;\n    performanceScore: number;\n    createdAt: Date;\n    updatedAt: Date;\n}\n\nexport interface ProjectContext {\n    id: string;\n    filePath: string;\n    content: string;\n    language: string;\n    framework?: string;\n    dependencies: string[];\n    imports: string[];\n    exports: string[];\n    components: string[];\n    embedding: Float32Array;\n    semanticChunks: SemanticChunk[];\n    lastModified: Date;\n}\n\nexport interface SemanticChunk {\n    id: string;\n    type: 'component' | 'hook' | 'utility' | 'style' | 'type';\n    name: string;\n    content: string;\n    startLine: number;\n    endLine: number;\n    embedding: Float32Array;\n    dependencies: string[];\n}\n\nexport interface SearchResult {\n    item: UIPattern | ProjectContext;\n    similarity: number;\n    type: 'pattern' | 'context';\n}\n\nexport class VectorDatabase {\n    private db: any; // Database instance\n    private patterns: Map<string, UIPattern> = new Map();\n    private contexts: Map<string, ProjectContext> = new Map();\n    private dbPath: string;\n    private isInitialized: boolean = false;\n\n    constructor(private extensionContext: vscode.ExtensionContext) {\n        this.dbPath = path.join(extensionContext.globalStorageUri.fsPath, 'uiorbit.db');\n        this.initializeDatabase();\n    }\n\n    private async initializeDatabase(): Promise<void> {\n        try {\n            // For now, use JSON fallback until SQLite is properly installed\n            await this.loadDatabase();\n            this.initializeDefaultPatterns();\n            this.isInitialized = true;\n        } catch (error) {\n            console.error('Failed to initialize UIOrbit database:', error);\n            // Fallback to in-memory storage\n            this.isInitialized = true;\n        }\n    }\n\n    private async loadDatabase(): Promise<void> {\n        try {\n            if (fs.existsSync(this.dbPath)) {\n                const data = JSON.parse(fs.readFileSync(this.dbPath, 'utf8'));\n                \n                if (data.patterns) {\n                    for (const pattern of data.patterns) {\n                        this.patterns.set(pattern.id, {\n                            ...pattern,\n                            created_at: new Date(pattern.created_at),\n                            updated_at: new Date(pattern.updated_at)\n                        });\n                    }\n                }\n\n                if (data.contexts) {\n                    for (const context of data.contexts) {\n                        this.contexts.set(context.id, {\n                            ...context,\n                            last_modified: new Date(context.last_modified)\n                        });\n                    }\n                }\n            }\n        } catch (error) {\n            console.error('Failed to load UIOrbit database:', error);\n        }\n    }\n\n    private async saveDatabase(): Promise<void> {\n        try {\n            // Ensure directory exists\n            const dir = path.dirname(this.dbPath);\n            if (!fs.existsSync(dir)) {\n                fs.mkdirSync(dir, { recursive: true });\n            }\n\n            const data = {\n                patterns: Array.from(this.patterns.values()),\n                contexts: Array.from(this.contexts.values()),\n                version: '1.0.0',\n                last_updated: new Date().toISOString()\n            };\n\n            fs.writeFileSync(this.dbPath, JSON.stringify(data, null, 2));\n        } catch (error) {\n            console.error('Failed to save UIOrbit database:', error);\n        }\n    }\n\n    private initializeDefaultPatterns(): void {\n        const defaultPatterns: Omit<UIPattern, 'id' | 'createdAt' | 'updatedAt'>[] = [\n            {\n                name: \"React Button Component\",\n                description: \"Modern, accessible button component with variants\",\n                framework: \"React\",\n                category: \"component\",\n                tags: [\"button\", \"interactive\", \"accessible\"],\n                code: `interface ButtonProps {\n  variant?: 'primary' | 'secondary' | 'outline';\n  size?: 'sm' | 'md' | 'lg';\n  disabled?: boolean;\n  children: React.ReactNode;\n  onClick?: () => void;\n}\n\nexport const Button: React.FC<ButtonProps> = ({\n  variant = 'primary',\n  size = 'md',\n  disabled = false,\n  children,\n  onClick\n}) => {\n  const baseClasses = 'font-medium rounded-lg transition-colors focus:outline-none focus:ring-2';\n  const variantClasses = {\n    primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',\n    secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',\n    outline: 'border-2 border-blue-600 text-blue-600 hover:bg-blue-50 focus:ring-blue-500'\n  };\n  const sizeClasses = {\n    sm: 'px-3 py-1.5 text-sm',\n    md: 'px-4 py-2 text-base',\n    lg: 'px-6 py-3 text-lg'\n  };\n\n  return (\n    <button\n      className={\\`\\${baseClasses} \\${variantClasses[variant]} \\${sizeClasses[size]} \\${disabled ? 'opacity-50 cursor-not-allowed' : ''}\\`}\n      disabled={disabled}\n      onClick={onClick}\n      aria-disabled={disabled}\n    >\n      {children}\n    </button>\n  );\n};`,\n                usageCount: 0,\n                embedding: new Float32Array(384), // Default embedding size\n                trendScore: 0.8,\n                accessibilityScore: 0.9,\n                performanceScore: 0.85\n            },\n            {\n                name: \"Responsive Card Layout\",\n                description: \"Flexible card component with responsive design\",\n                framework: \"React\",\n                category: \"layout\",\n                tags: [\"card\", \"responsive\", \"layout\"],\n                code: `interface CardProps {\n  title?: string;\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const Card: React.FC<CardProps> = ({ title, children, className = '' }) => {\n  return (\n    <div className={\\`bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden \\${className}\\`}>\n      {title && (\n        <div className=\"px-6 py-4 border-b border-gray-200\">\n          <h3 className=\"text-lg font-semibold text-gray-900\">{title}</h3>\n        </div>\n      )}\n      <div className=\"p-6\">\n        {children}\n      </div>\n    </div>\n  );\n};`,\n                usageCount: 0,\n                embedding: new Float32Array(384),\n                trendScore: 0.7,\n                accessibilityScore: 0.8,\n                performanceScore: 0.9\n            }\n        ];\n\n        for (const pattern of defaultPatterns) {\n            const id = this.generateId();\n            const now = new Date();\n            this.patterns.set(id, {\n                ...pattern,\n                id,\n                createdAt: now,\n                updatedAt: now\n            });\n        }\n\n        this.saveDatabase();\n    }\n\n    public addPattern(pattern: Omit<UIPattern, 'id' | 'createdAt' | 'updatedAt'>): string {\n        const id = this.generateId();\n        const now = new Date();\n\n        const newPattern: UIPattern = {\n            ...pattern,\n            id,\n            createdAt: now,\n            updatedAt: now\n        };\n\n        this.patterns.set(id, newPattern);\n        this.saveDatabase();\n        return id;\n    }\n\n    public getPattern(id: string): UIPattern | undefined {\n        return this.patterns.get(id);\n    }\n\n    public searchPatterns(query: string, framework?: string, category?: string): UIPattern[] {\n        const results: UIPattern[] = [];\n        const queryLower = query.toLowerCase();\n\n        for (const pattern of this.patterns.values()) {\n            // Filter by framework if specified\n            if (framework && pattern.framework !== framework) {\n                continue;\n            }\n\n            // Filter by category if specified\n            if (category && pattern.category !== category) {\n                continue;\n            }\n\n            // Search in name, description, and tags\n            const searchText = `${pattern.name} ${pattern.description} ${pattern.tags.join(' ')}`.toLowerCase();\n            if (searchText.includes(queryLower)) {\n                results.push(pattern);\n            }\n        }\n\n        // Sort by usage count (most used first)\n        return results.sort((a, b) => b.usageCount - a.usageCount);\n    }\n\n    public addProjectContext(context: Omit<ProjectContext, 'id'>): string {\n        const id = this.generateId();\n        const newContext: ProjectContext = {\n            ...context,\n            id\n        };\n\n        this.contexts.set(id, newContext);\n        this.saveDatabase();\n        return id;\n    }\n\n    public getProjectContext(filePath: string): ProjectContext | undefined {\n        for (const context of this.contexts.values()) {\n            if (context.filePath === filePath) {\n                return context;\n            }\n        }\n        return undefined;\n    }\n\n    public updatePatternUsage(id: string): void {\n        const pattern = this.patterns.get(id);\n        if (pattern) {\n            pattern.usageCount++;\n            pattern.updatedAt = new Date();\n            this.saveDatabase();\n        }\n    }\n\n    public getPopularPatterns(limit: number = 10): UIPattern[] {\n        return Array.from(this.patterns.values())\n            .sort((a, b) => b.usageCount - a.usageCount)\n            .slice(0, limit);\n    }\n\n    public getPatternsByFramework(framework: string): UIPattern[] {\n        return Array.from(this.patterns.values())\n            .filter(pattern => pattern.framework === framework);\n    }\n\n    private generateId(): string {\n        return Math.random().toString(36).substring(2, 11);\n    }\n\n    public cleanup(): void {\n        // Remove old contexts (older than 30 days)\n        const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);\n\n        for (const [id, context] of this.contexts.entries()) {\n            if (context.lastModified < thirtyDaysAgo) {\n                this.contexts.delete(id);\n            }\n        }\n\n        this.saveDatabase();\n    }\n\n    // Enhanced methods for Augment Code-style functionality\n    public async semanticSearch(query: string, limit: number = 10): Promise<SearchResult[]> {\n        // For now, use simple text matching until we implement proper embeddings\n        const results: SearchResult[] = [];\n\n        // Search patterns\n        for (const pattern of this.patterns.values()) {\n            const searchText = `${pattern.name} ${pattern.description} ${pattern.tags.join(' ')} ${pattern.code}`.toLowerCase();\n            if (searchText.includes(query.toLowerCase())) {\n                results.push({\n                    item: pattern,\n                    similarity: this.calculateTextSimilarity(query, searchText),\n                    type: 'pattern'\n                });\n            }\n        }\n\n        // Search project contexts\n        for (const context of this.contexts.values()) {\n            const searchText = `${context.filePath} ${context.content} ${context.components.join(' ')}`.toLowerCase();\n            if (searchText.includes(query.toLowerCase())) {\n                results.push({\n                    item: context,\n                    similarity: this.calculateTextSimilarity(query, searchText),\n                    type: 'context'\n                });\n            }\n        }\n\n        return results\n            .sort((a, b) => b.similarity - a.similarity)\n            .slice(0, limit);\n    }\n\n    public getRelevantContext(filePath: string, _query: string): ProjectContext[] {\n        const contexts: ProjectContext[] = [];\n        const currentDir = path.dirname(filePath);\n\n        for (const context of this.contexts.values()) {\n            const contextDir = path.dirname(context.filePath);\n\n            // Prioritize files in the same directory\n            if (contextDir === currentDir) {\n                contexts.push(context);\n            }\n            // Include related files based on imports/exports\n            else if (this.areFilesRelated(filePath, context.filePath)) {\n                contexts.push(context);\n            }\n        }\n\n        return contexts.slice(0, 5); // Limit to 5 most relevant contexts\n    }\n\n    private calculateTextSimilarity(query: string, text: string): number {\n        const queryWords = query.toLowerCase().split(/\\s+/);\n        const textWords = text.toLowerCase().split(/\\s+/);\n\n        let matches = 0;\n        for (const word of queryWords) {\n            if (textWords.some(textWord => textWord.includes(word))) {\n                matches++;\n            }\n        }\n\n        return matches / queryWords.length;\n    }\n\n    private areFilesRelated(file1: string, file2: string): boolean {\n        const context1 = this.getProjectContext(file1);\n        const context2 = this.getProjectContext(file2);\n\n        if (!context1 || !context2) {\n            return false;\n        }\n\n        // Check if they share imports/exports\n        const sharedDeps = context1.dependencies.filter(dep =>\n            context2.dependencies.includes(dep)\n        );\n\n        return sharedDeps.length > 0;\n    }\n\n    public getTrendingPatterns(): UIPattern[] {\n        return Array.from(this.patterns.values())\n            .sort((a, b) => b.trendScore - a.trendScore)\n            .slice(0, 10);\n    }\n\n    public getAccessiblePatterns(): UIPattern[] {\n        return Array.from(this.patterns.values())\n            .filter(pattern => pattern.accessibilityScore > 0.7)\n            .sort((a, b) => b.accessibilityScore - a.accessibilityScore);\n    }\n\n    public getPerformantPatterns(): UIPattern[] {\n        return Array.from(this.patterns.values())\n            .filter(pattern => pattern.performanceScore > 0.8)\n            .sort((a, b) => b.performanceScore - a.performanceScore);\n    }\n}\n", "/**\n * UIOrbit - File System Watcher for real-time context updates\n * Monitors project files and updates the vector database with changes\n */\n\nimport * as vscode from 'vscode';\nimport { VectorDatabase, ProjectContext, SemanticChunk } from './vector-database';\nimport { ASTAnalyzer } from './ast-analyzer';\n\nexport interface FileChangeEvent {\n    type: 'created' | 'modified' | 'deleted';\n    filePath: string;\n    timestamp: Date;\n}\n\nexport class FileWatcher {\n    private watchers: vscode.FileSystemWatcher[] = [];\n    private vectorDb: VectorDatabase;\n    private astAnalyzer: ASTAnalyzer;\n    private changeQueue: FileChangeEvent[] = [];\n    private processingTimer: NodeJS.Timeout | null = null;\n\n    constructor(\n        private extensionContext: vscode.ExtensionContext,\n        vectorDb: VectorDatabase\n    ) {\n        this.vectorDb = vectorDb;\n        this.astAnalyzer = new ASTAnalyzer();\n        this.initializeWatchers();\n    }\n\n    private initializeWatchers(): void {\n        // Watch for UI-related file changes\n        const patterns = [\n            '**/*.{tsx,jsx,ts,js}',\n            '**/*.vue',\n            '**/*.svelte',\n            '**/*.css',\n            '**/*.scss',\n            '**/*.sass',\n            '**/*.less',\n            '**/package.json',\n            '**/tsconfig.json',\n            '**/tailwind.config.js',\n            '**/next.config.js',\n            '**/vite.config.js'\n        ];\n\n        for (const pattern of patterns) {\n            const watcher = vscode.workspace.createFileSystemWatcher(pattern);\n            \n            watcher.onDidCreate(uri => this.handleFileChange('created', uri.fsPath));\n            watcher.onDidChange(uri => this.handleFileChange('modified', uri.fsPath));\n            watcher.onDidDelete(uri => this.handleFileChange('deleted', uri.fsPath));\n            \n            this.watchers.push(watcher);\n        }\n\n        // Watch for active editor changes\n        vscode.window.onDidChangeActiveTextEditor(editor => {\n            if (editor && this.isUIFile(editor.document)) {\n                this.handleActiveFileChange(editor.document);\n            }\n        });\n\n        // Watch for document saves\n        vscode.workspace.onDidSaveTextDocument(document => {\n            if (this.isUIFile(document)) {\n                this.handleFileChange('modified', document.uri.fsPath);\n            }\n        });\n    }\n\n    private handleFileChange(type: FileChangeEvent['type'], filePath: string): void {\n        // Skip node_modules and other irrelevant directories\n        if (this.shouldIgnoreFile(filePath)) {\n            return;\n        }\n\n        const event: FileChangeEvent = {\n            type,\n            filePath,\n            timestamp: new Date()\n        };\n\n        this.changeQueue.push(event);\n        this.scheduleProcessing();\n    }\n\n    private scheduleProcessing(): void {\n        // Debounce file changes to avoid excessive processing\n        if (this.processingTimer) {\n            clearTimeout(this.processingTimer);\n        }\n\n        this.processingTimer = setTimeout(() => {\n            this.processChangeQueue();\n        }, 1000); // Wait 1 second after last change\n    }\n\n    private async processChangeQueue(): Promise<void> {\n        if (this.changeQueue.length === 0) return;\n\n        const events = [...this.changeQueue];\n        this.changeQueue = [];\n\n        for (const event of events) {\n            try {\n                await this.processFileChange(event);\n            } catch (error) {\n                console.error(`Error processing file change for ${event.filePath}:`, error);\n            }\n        }\n    }\n\n    private async processFileChange(event: FileChangeEvent): Promise<void> {\n        switch (event.type) {\n            case 'created':\n            case 'modified':\n                await this.updateFileContext(event.filePath);\n                break;\n            case 'deleted':\n                await this.removeFileContext(event.filePath);\n                break;\n        }\n    }\n\n    private async updateFileContext(filePath: string): Promise<void> {\n        try {\n            const componentInfo = await this.astAnalyzer.analyzeFile(filePath);\n            if (!componentInfo) return;\n\n            const document = await vscode.workspace.openTextDocument(filePath);\n            const content = document.getText();\n            \n            const context: Omit<ProjectContext, 'id'> = {\n                filePath: filePath,\n                content: content.substring(0, 10000), // Limit content size\n                language: document.languageId,\n                framework: componentInfo.framework,\n                dependencies: componentInfo.dependencies,\n                imports: componentInfo.imports,\n                exports: componentInfo.exports,\n                components: [componentInfo.name],\n                embedding: new Float32Array(384), // Placeholder embedding\n                semanticChunks: [], // Will be populated by enhanced chunking\n                lastModified: new Date()\n            };\n\n            // Remove existing context for this file\n            const existingContext = this.vectorDb.getProjectContext(filePath);\n            if (existingContext) {\n                // Update existing context\n                Object.assign(existingContext, context);\n            } else {\n                // Add new context\n                this.vectorDb.addProjectContext(context);\n            }\n\n        } catch (error) {\n            console.error(`Error updating context for ${filePath}:`, error);\n        }\n    }\n\n    private async removeFileContext(filePath: string): Promise<void> {\n        // Note: VectorDatabase would need a removeProjectContext method\n        // For now, we'll just log the deletion\n        console.log(`File deleted: ${filePath}`);\n    }\n\n    private async handleActiveFileChange(document: vscode.TextDocument): Promise<void> {\n        // Update context when user switches to a UI file\n        if (this.isUIFile(document)) {\n            await this.updateFileContext(document.uri.fsPath);\n        }\n    }\n\n    private isUIFile(document: vscode.TextDocument): boolean {\n        const uiExtensions = ['.tsx', '.jsx', '.ts', '.js', '.vue', '.svelte'];\n        const uiLanguages = ['typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'vue', 'svelte'];\n        \n        return uiExtensions.some(ext => document.fileName.endsWith(ext)) ||\n               uiLanguages.includes(document.languageId);\n    }\n\n    private shouldIgnoreFile(filePath: string): boolean {\n        const ignorePaths = [\n            'node_modules',\n            '.git',\n            'dist',\n            'build',\n            '.next',\n            '.nuxt',\n            'coverage',\n            '.vscode',\n            '.idea'\n        ];\n\n        return ignorePaths.some(ignore => filePath.includes(ignore));\n    }\n\n    public getRecentChanges(limit: number = 10): FileChangeEvent[] {\n        return this.changeQueue\n            .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())\n            .slice(0, limit);\n    }\n\n    public async getProjectSummary(): Promise<{\n        totalFiles: number;\n        recentChanges: number;\n        frameworks: string[];\n        components: number;\n    }> {\n        const projectAnalysis = await this.astAnalyzer.analyzeProject();\n        const recentChanges = this.changeQueue.filter(\n            event => event.timestamp > new Date(Date.now() - 24 * 60 * 60 * 1000)\n        ).length;\n\n        return {\n            totalFiles: projectAnalysis.fileStructure.length,\n            recentChanges,\n            frameworks: [projectAnalysis.framework, projectAnalysis.stylingFramework],\n            components: projectAnalysis.components.length\n        };\n    }\n\n    public async getContextForCurrentFile(): Promise<{\n        currentFile?: ProjectContext;\n        relatedFiles: ProjectContext[];\n        suggestions: string[];\n    }> {\n        const activeEditor = vscode.window.activeTextEditor;\n        if (!activeEditor) {\n            return { relatedFiles: [], suggestions: [] };\n        }\n\n        const currentFile = this.vectorDb.getProjectContext(activeEditor.document.uri.fsPath);\n        const relatedFiles: ProjectContext[] = [];\n        const suggestions: string[] = [];\n\n        if (currentFile) {\n            // Find related files based on imports/exports\n            // This is a simplified implementation\n            suggestions.push(\n                'Consider adding responsive design',\n                'Add accessibility features',\n                'Apply modern styling patterns'\n            );\n        }\n\n        return {\n            currentFile,\n            relatedFiles,\n            suggestions\n        };\n    }\n\n    public dispose(): void {\n        // Clean up watchers\n        for (const watcher of this.watchers) {\n            watcher.dispose();\n        }\n        this.watchers = [];\n\n        // Clear processing timer\n        if (this.processingTimer) {\n            clearTimeout(this.processingTimer);\n            this.processingTimer = null;\n        }\n    }\n}\n", "/**\n * UIOrbit - AST-based code analysis for better context understanding\n * This module analyzes code structure to provide intelligent UI suggestions\n */\n\nimport * as vscode from 'vscode';\nimport * as path from 'path';\n\nexport interface ComponentInfo {\n    name: string;\n    type: 'functional' | 'class' | 'hook' | 'utility';\n    props?: string[];\n    imports: string[];\n    exports: string[];\n    dependencies: string[];\n    framework: string;\n    hasStyles: boolean;\n    isResponsive: boolean;\n    hasAccessibility: boolean;\n    complexity: 'low' | 'medium' | 'high';\n}\n\nexport interface ProjectAnalysis {\n    framework: string;\n    stylingFramework: string;\n    components: ComponentInfo[];\n    dependencies: string[];\n    fileStructure: string[];\n    designPatterns: string[];\n    accessibility: boolean;\n    responsive: boolean;\n}\n\nexport class ASTAnalyzer {\n    private workspaceRoot: string;\n\n    constructor() {\n        this.workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '';\n    }\n\n    public async analyzeProject(): Promise<ProjectAnalysis> {\n        const packageJson = await this.readPackageJson();\n        const framework = this.detectFramework(packageJson);\n        const stylingFramework = this.detectStylingFramework(packageJson);\n        \n        const components = await this.analyzeComponents();\n        const dependencies = Object.keys(packageJson?.dependencies || {});\n        const fileStructure = await this.getFileStructure();\n        const designPatterns = this.detectDesignPatterns(components);\n        \n        return {\n            framework,\n            stylingFramework,\n            components,\n            dependencies,\n            fileStructure,\n            designPatterns,\n            accessibility: this.hasAccessibilityFeatures(components, dependencies),\n            responsive: this.hasResponsiveDesign(components, dependencies)\n        };\n    }\n\n    public async analyzeFile(filePath: string): Promise<ComponentInfo | null> {\n        try {\n            const document = await vscode.workspace.openTextDocument(filePath);\n            const content = document.getText();\n            const language = document.languageId;\n\n            if (!this.isUIFile(language, content)) {\n                return null;\n            }\n\n            return this.parseComponent(content, language, path.basename(filePath));\n        } catch (error) {\n            console.error('Error analyzing file:', error);\n            return null;\n        }\n    }\n\n    private async readPackageJson(): Promise<any> {\n        try {\n            const packagePath = path.join(this.workspaceRoot, 'package.json');\n            const document = await vscode.workspace.openTextDocument(packagePath);\n            return JSON.parse(document.getText());\n        } catch {\n            return {};\n        }\n    }\n\n    private detectFramework(packageJson: any): string {\n        const deps = { ...packageJson.dependencies, ...packageJson.devDependencies };\n        \n        if (deps.react) return 'React';\n        if (deps.vue) return 'Vue';\n        if (deps['@angular/core']) return 'Angular';\n        if (deps.svelte) return 'Svelte';\n        if (deps.next) return 'Next.js';\n        if (deps.nuxt) return 'Nuxt.js';\n        \n        return 'HTML/CSS';\n    }\n\n    private detectStylingFramework(packageJson: any): string {\n        const deps = { ...packageJson.dependencies, ...packageJson.devDependencies };\n        \n        if (deps.tailwindcss || deps['@tailwindcss/forms']) return 'Tailwind CSS';\n        if (deps['styled-components']) return 'Styled Components';\n        if (deps['@emotion/react'] || deps['@emotion/styled']) return 'Emotion';\n        if (deps.sass || deps.scss) return 'SCSS/SASS';\n        if (deps['css-modules']) return 'CSS Modules';\n        if (deps['@mui/material']) return 'Material-UI';\n        if (deps.antd) return 'Ant Design';\n        if (deps['@chakra-ui/react']) return 'Chakra UI';\n        \n        return 'CSS';\n    }\n\n    private async analyzeComponents(): Promise<ComponentInfo[]> {\n        const components: ComponentInfo[] = [];\n        const files = await vscode.workspace.findFiles('**/*.{tsx,jsx,ts,js,vue,svelte}', '**/node_modules/**');\n        \n        for (const file of files.slice(0, 50)) { // Limit to avoid performance issues\n            const component = await this.analyzeFile(file.fsPath);\n            if (component) {\n                components.push(component);\n            }\n        }\n        \n        return components;\n    }\n\n    private async getFileStructure(): Promise<string[]> {\n        const files = await vscode.workspace.findFiles('**/*', '**/node_modules/**');\n        return files.map(file => vscode.workspace.asRelativePath(file)).slice(0, 100);\n    }\n\n    private isUIFile(language: string, content: string): boolean {\n        const uiLanguages = ['typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'vue', 'svelte'];\n        if (!uiLanguages.includes(language)) return false;\n\n        // Check for UI-related keywords\n        const uiKeywords = [\n            'component', 'jsx', 'tsx', 'render', 'return', 'div', 'span', 'button',\n            'input', 'form', 'className', 'style', 'props', 'useState', 'useEffect'\n        ];\n        \n        const contentLower = content.toLowerCase();\n        return uiKeywords.some(keyword => contentLower.includes(keyword));\n    }\n\n    private parseComponent(content: string, language: string, fileName: string): ComponentInfo {\n        const name = this.extractComponentName(content, fileName);\n        const type = this.determineComponentType(content);\n        const props = this.extractProps(content);\n        const imports = this.extractImports(content);\n        const exports = this.extractExports(content);\n        const dependencies = this.extractDependencies(content);\n        const framework = this.detectFrameworkFromContent(content);\n        \n        return {\n            name,\n            type,\n            props,\n            imports,\n            exports,\n            dependencies,\n            framework,\n            hasStyles: this.hasStyles(content),\n            isResponsive: this.isResponsive(content),\n            hasAccessibility: this.hasAccessibility(content),\n            complexity: this.calculateComplexity(content)\n        };\n    }\n\n    private extractComponentName(content: string, fileName: string): string {\n        // Try to extract from function/class declarations\n        const functionMatch = content.match(/(?:function|const|class)\\s+([A-Z][a-zA-Z0-9]*)/);\n        if (functionMatch) return functionMatch[1];\n        \n        // Fallback to filename\n        return fileName.replace(/\\.(tsx?|jsx?|vue|svelte)$/, '');\n    }\n\n    private determineComponentType(content: string): ComponentInfo['type'] {\n        if (content.includes('class ') && content.includes('extends')) return 'class';\n        if (content.includes('use') && content.match(/use[A-Z]/)) return 'hook';\n        if (content.includes('function') || content.includes('const') || content.includes('=>')) return 'functional';\n        return 'utility';\n    }\n\n    private extractProps(content: string): string[] {\n        const props: string[] = [];\n        \n        // Extract from TypeScript interfaces\n        const interfaceMatch = content.match(/interface\\s+\\w*Props\\s*{([^}]*)}/s);\n        if (interfaceMatch) {\n            const propsContent = interfaceMatch[1];\n            const propMatches = propsContent.match(/(\\w+)(?:\\?)?:/g);\n            if (propMatches) {\n                props.push(...propMatches.map(match => match.replace(/[?:]/g, '')));\n            }\n        }\n        \n        // Extract from destructuring\n        const destructureMatch = content.match(/{\\s*([^}]+)\\s*}/);\n        if (destructureMatch) {\n            const destructured = destructureMatch[1].split(',').map(prop => prop.trim().split(':')[0].trim());\n            props.push(...destructured);\n        }\n        \n        return [...new Set(props)];\n    }\n\n    private extractImports(content: string): string[] {\n        const imports: string[] = [];\n        const importMatches = content.match(/import\\s+.*?from\\s+['\"]([^'\"]+)['\"]/g);\n        \n        if (importMatches) {\n            for (const match of importMatches) {\n                const moduleMatch = match.match(/from\\s+['\"]([^'\"]+)['\"]/);\n                if (moduleMatch) {\n                    imports.push(moduleMatch[1]);\n                }\n            }\n        }\n        \n        return imports;\n    }\n\n    private extractExports(content: string): string[] {\n        const exports: string[] = [];\n        const exportMatches = content.match(/export\\s+(?:default\\s+)?(?:function|const|class|interface|type)\\s+(\\w+)/g);\n        \n        if (exportMatches) {\n            for (const match of exportMatches) {\n                const nameMatch = match.match(/(?:function|const|class|interface|type)\\s+(\\w+)/);\n                if (nameMatch) {\n                    exports.push(nameMatch[1]);\n                }\n            }\n        }\n        \n        return exports;\n    }\n\n    private extractDependencies(content: string): string[] {\n        const deps: string[] = [];\n        const imports = this.extractImports(content);\n        \n        for (const imp of imports) {\n            if (!imp.startsWith('.') && !imp.startsWith('/')) {\n                deps.push(imp.split('/')[0]);\n            }\n        }\n        \n        return [...new Set(deps)];\n    }\n\n    private detectFrameworkFromContent(content: string): string {\n        if (content.includes('React') || content.includes('jsx') || content.includes('useState')) return 'React';\n        if (content.includes('Vue') || content.includes('<template>')) return 'Vue';\n        if (content.includes('@Component') || content.includes('Angular')) return 'Angular';\n        if (content.includes('svelte') || content.includes('<script>')) return 'Svelte';\n        return 'JavaScript';\n    }\n\n    private hasStyles(content: string): boolean {\n        return content.includes('className') || \n               content.includes('style=') || \n               content.includes('styled') ||\n               content.includes('css`') ||\n               content.includes('.module.css');\n    }\n\n    private isResponsive(content: string): boolean {\n        const responsiveKeywords = ['responsive', 'mobile', 'tablet', 'desktop', 'breakpoint', 'media', 'sm:', 'md:', 'lg:', 'xl:'];\n        return responsiveKeywords.some(keyword => content.toLowerCase().includes(keyword));\n    }\n\n    private hasAccessibility(content: string): boolean {\n        const a11yKeywords = ['aria-', 'role=', 'tabIndex', 'alt=', 'label', 'accessibility', 'screen reader'];\n        return a11yKeywords.some(keyword => content.toLowerCase().includes(keyword));\n    }\n\n    private calculateComplexity(content: string): ComponentInfo['complexity'] {\n        const lines = content.split('\\n').length;\n        const cyclomaticComplexity = (content.match(/if|else|for|while|switch|case|\\?|&&|\\|\\|/g) || []).length;\n        \n        if (lines > 200 || cyclomaticComplexity > 15) return 'high';\n        if (lines > 100 || cyclomaticComplexity > 8) return 'medium';\n        return 'low';\n    }\n\n    private detectDesignPatterns(components: ComponentInfo[]): string[] {\n        const patterns: string[] = [];\n        \n        // Check for common patterns\n        if (components.some(c => c.name.includes('Provider'))) patterns.push('Provider Pattern');\n        if (components.some(c => c.name.includes('Hook'))) patterns.push('Custom Hooks');\n        if (components.some(c => c.name.includes('HOC'))) patterns.push('Higher-Order Components');\n        if (components.some(c => c.name.includes('Container'))) patterns.push('Container/Presentational');\n        \n        return patterns;\n    }\n\n    private hasAccessibilityFeatures(components: ComponentInfo[], dependencies: string[]): boolean {\n        return components.some(c => c.hasAccessibility) || \n               dependencies.some(dep => dep.includes('a11y') || dep.includes('accessibility'));\n    }\n\n    private hasResponsiveDesign(components: ComponentInfo[], dependencies: string[]): boolean {\n        return components.some(c => c.isResponsive) || \n               dependencies.includes('tailwindcss') ||\n               dependencies.includes('@media');\n    }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAAAA,UAAwB;;;ACKxB,IAAAC,UAAwB;;;ACGxB,SAAoB;AAEpB,WAAsB;AAuDf,IAAM,iBAAN,MAAqB;AAAA,EAOxB,YAAoB,kBAA2C;AAA3C;AALpB,SAAQ,WAAmC,oBAAI,IAAI;AACnD,SAAQ,WAAwC,oBAAI,IAAI;AAExD,SAAQ,gBAAyB;AAG7B,SAAK,SAAc,UAAK,iBAAiB,iBAAiB,QAAQ,YAAY;AAC9E,SAAK,mBAAmB;AAAA,EAC5B;AAAA,EAEA,MAAc,qBAAoC;AAC9C,QAAI;AAEA,YAAM,KAAK,aAAa;AACxB,WAAK,0BAA0B;AAC/B,WAAK,gBAAgB;AAAA,IACzB,SAAS,OAAP;AACE,cAAQ,MAAM,0CAA0C,KAAK;AAE7D,WAAK,gBAAgB;AAAA,IACzB;AAAA,EACJ;AAAA,EAEA,MAAc,eAA8B;AACxC,QAAI;AACA,UAAO,cAAW,KAAK,MAAM,GAAG;AAC5B,cAAM,OAAO,KAAK,MAAS,gBAAa,KAAK,QAAQ,MAAM,CAAC;AAE5D,YAAI,KAAK,UAAU;AACf,qBAAW,WAAW,KAAK,UAAU;AACjC,iBAAK,SAAS,IAAI,QAAQ,IAAI;AAAA,cAC1B,GAAG;AAAA,cACH,YAAY,IAAI,KAAK,QAAQ,UAAU;AAAA,cACvC,YAAY,IAAI,KAAK,QAAQ,UAAU;AAAA,YAC3C,CAAC;AAAA,UACL;AAAA,QACJ;AAEA,YAAI,KAAK,UAAU;AACf,qBAAW,WAAW,KAAK,UAAU;AACjC,iBAAK,SAAS,IAAI,QAAQ,IAAI;AAAA,cAC1B,GAAG;AAAA,cACH,eAAe,IAAI,KAAK,QAAQ,aAAa;AAAA,YACjD,CAAC;AAAA,UACL;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,SAAS,OAAP;AACE,cAAQ,MAAM,oCAAoC,KAAK;AAAA,IAC3D;AAAA,EACJ;AAAA,EAEA,MAAc,eAA8B;AACxC,QAAI;AAEA,YAAM,MAAW,aAAQ,KAAK,MAAM;AACpC,UAAI,CAAI,cAAW,GAAG,GAAG;AACrB,QAAG,aAAU,KAAK,EAAE,WAAW,KAAK,CAAC;AAAA,MACzC;AAEA,YAAM,OAAO;AAAA,QACT,UAAU,MAAM,KAAK,KAAK,SAAS,OAAO,CAAC;AAAA,QAC3C,UAAU,MAAM,KAAK,KAAK,SAAS,OAAO,CAAC;AAAA,QAC3C,SAAS;AAAA,QACT,cAAc,IAAI,KAAK,EAAE,YAAY;AAAA,MACzC;AAEA,MAAG,iBAAc,KAAK,QAAQ,KAAK,UAAU,MAAM,MAAM,CAAC,CAAC;AAAA,IAC/D,SAAS,OAAP;AACE,cAAQ,MAAM,oCAAoC,KAAK;AAAA,IAC3D;AAAA,EACJ;AAAA,EAEQ,4BAAkC;AACtC,UAAM,kBAAuE;AAAA,MACzE;AAAA,QACI,MAAM;AAAA,QACN,aAAa;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA,QACV,MAAM,CAAC,UAAU,eAAe,YAAY;AAAA,QAC5C,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAsCN,YAAY;AAAA,QACZ,WAAW,IAAI,aAAa,GAAG;AAAA,QAC/B,YAAY;AAAA,QACZ,oBAAoB;AAAA,QACpB,kBAAkB;AAAA,MACtB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,aAAa;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA,QACV,MAAM,CAAC,QAAQ,cAAc,QAAQ;AAAA,QACrC,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAoBN,YAAY;AAAA,QACZ,WAAW,IAAI,aAAa,GAAG;AAAA,QAC/B,YAAY;AAAA,QACZ,oBAAoB;AAAA,QACpB,kBAAkB;AAAA,MACtB;AAAA,IACJ;AAEA,eAAW,WAAW,iBAAiB;AACnC,YAAM,KAAK,KAAK,WAAW;AAC3B,YAAM,MAAM,IAAI,KAAK;AACrB,WAAK,SAAS,IAAI,IAAI;AAAA,QAClB,GAAG;AAAA,QACH;AAAA,QACA,WAAW;AAAA,QACX,WAAW;AAAA,MACf,CAAC;AAAA,IACL;AAEA,SAAK,aAAa;AAAA,EACtB;AAAA,EAEO,WAAW,SAAoE;AAClF,UAAM,KAAK,KAAK,WAAW;AAC3B,UAAM,MAAM,IAAI,KAAK;AAErB,UAAM,aAAwB;AAAA,MAC1B,GAAG;AAAA,MACH;AAAA,MACA,WAAW;AAAA,MACX,WAAW;AAAA,IACf;AAEA,SAAK,SAAS,IAAI,IAAI,UAAU;AAChC,SAAK,aAAa;AAClB,WAAO;AAAA,EACX;AAAA,EAEO,WAAW,IAAmC;AACjD,WAAO,KAAK,SAAS,IAAI,EAAE;AAAA,EAC/B;AAAA,EAEO,eAAe,OAAe,WAAoB,UAAgC;AACrF,UAAM,UAAuB,CAAC;AAC9B,UAAM,aAAa,MAAM,YAAY;AAErC,eAAW,WAAW,KAAK,SAAS,OAAO,GAAG;AAE1C,UAAI,aAAa,QAAQ,cAAc,WAAW;AAC9C;AAAA,MACJ;AAGA,UAAI,YAAY,QAAQ,aAAa,UAAU;AAC3C;AAAA,MACJ;AAGA,YAAM,aAAa,GAAG,QAAQ,QAAQ,QAAQ,eAAe,QAAQ,KAAK,KAAK,GAAG,IAAI,YAAY;AAClG,UAAI,WAAW,SAAS,UAAU,GAAG;AACjC,gBAAQ,KAAK,OAAO;AAAA,MACxB;AAAA,IACJ;AAGA,WAAO,QAAQ,KAAK,CAAC,GAAG,MAAM,EAAE,aAAa,EAAE,UAAU;AAAA,EAC7D;AAAA,EAEO,kBAAkB,SAA6C;AAClE,UAAM,KAAK,KAAK,WAAW;AAC3B,UAAM,aAA6B;AAAA,MAC/B,GAAG;AAAA,MACH;AAAA,IACJ;AAEA,SAAK,SAAS,IAAI,IAAI,UAAU;AAChC,SAAK,aAAa;AAClB,WAAO;AAAA,EACX;AAAA,EAEO,kBAAkB,UAA8C;AACnE,eAAW,WAAW,KAAK,SAAS,OAAO,GAAG;AAC1C,UAAI,QAAQ,aAAa,UAAU;AAC/B,eAAO;AAAA,MACX;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA,EAEO,mBAAmB,IAAkB;AACxC,UAAM,UAAU,KAAK,SAAS,IAAI,EAAE;AACpC,QAAI,SAAS;AACT,cAAQ;AACR,cAAQ,YAAY,IAAI,KAAK;AAC7B,WAAK,aAAa;AAAA,IACtB;AAAA,EACJ;AAAA,EAEO,mBAAmB,QAAgB,IAAiB;AACvD,WAAO,MAAM,KAAK,KAAK,SAAS,OAAO,CAAC,EACnC,KAAK,CAAC,GAAG,MAAM,EAAE,aAAa,EAAE,UAAU,EAC1C,MAAM,GAAG,KAAK;AAAA,EACvB;AAAA,EAEO,uBAAuB,WAAgC;AAC1D,WAAO,MAAM,KAAK,KAAK,SAAS,OAAO,CAAC,EACnC,OAAO,aAAW,QAAQ,cAAc,SAAS;AAAA,EAC1D;AAAA,EAEQ,aAAqB;AACzB,WAAO,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,UAAU,GAAG,EAAE;AAAA,EACrD;AAAA,EAEO,UAAgB;AAEnB,UAAM,gBAAgB,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,GAAI;AAEpE,eAAW,CAAC,IAAI,OAAO,KAAK,KAAK,SAAS,QAAQ,GAAG;AACjD,UAAI,QAAQ,eAAe,eAAe;AACtC,aAAK,SAAS,OAAO,EAAE;AAAA,MAC3B;AAAA,IACJ;AAEA,SAAK,aAAa;AAAA,EACtB;AAAA,EAGA,MAAa,eAAe,OAAe,QAAgB,IAA6B;AAEpF,UAAM,UAA0B,CAAC;AAGjC,eAAW,WAAW,KAAK,SAAS,OAAO,GAAG;AAC1C,YAAM,aAAa,GAAG,QAAQ,QAAQ,QAAQ,eAAe,QAAQ,KAAK,KAAK,GAAG,KAAK,QAAQ,OAAO,YAAY;AAClH,UAAI,WAAW,SAAS,MAAM,YAAY,CAAC,GAAG;AAC1C,gBAAQ,KAAK;AAAA,UACT,MAAM;AAAA,UACN,YAAY,KAAK,wBAAwB,OAAO,UAAU;AAAA,UAC1D,MAAM;AAAA,QACV,CAAC;AAAA,MACL;AAAA,IACJ;AAGA,eAAW,WAAW,KAAK,SAAS,OAAO,GAAG;AAC1C,YAAM,aAAa,GAAG,QAAQ,YAAY,QAAQ,WAAW,QAAQ,WAAW,KAAK,GAAG,IAAI,YAAY;AACxG,UAAI,WAAW,SAAS,MAAM,YAAY,CAAC,GAAG;AAC1C,gBAAQ,KAAK;AAAA,UACT,MAAM;AAAA,UACN,YAAY,KAAK,wBAAwB,OAAO,UAAU;AAAA,UAC1D,MAAM;AAAA,QACV,CAAC;AAAA,MACL;AAAA,IACJ;AAEA,WAAO,QACF,KAAK,CAAC,GAAG,MAAM,EAAE,aAAa,EAAE,UAAU,EAC1C,MAAM,GAAG,KAAK;AAAA,EACvB;AAAA,EAEO,mBAAmB,UAAkB,QAAkC;AAC1E,UAAM,WAA6B,CAAC;AACpC,UAAM,aAAkB,aAAQ,QAAQ;AAExC,eAAW,WAAW,KAAK,SAAS,OAAO,GAAG;AAC1C,YAAM,aAAkB,aAAQ,QAAQ,QAAQ;AAGhD,UAAI,eAAe,YAAY;AAC3B,iBAAS,KAAK,OAAO;AAAA,MACzB,WAES,KAAK,gBAAgB,UAAU,QAAQ,QAAQ,GAAG;AACvD,iBAAS,KAAK,OAAO;AAAA,MACzB;AAAA,IACJ;AAEA,WAAO,SAAS,MAAM,GAAG,CAAC;AAAA,EAC9B;AAAA,EAEQ,wBAAwB,OAAe,MAAsB;AACjE,UAAM,aAAa,MAAM,YAAY,EAAE,MAAM,KAAK;AAClD,UAAM,YAAY,KAAK,YAAY,EAAE,MAAM,KAAK;AAEhD,QAAI,UAAU;AACd,eAAW,QAAQ,YAAY;AAC3B,UAAI,UAAU,KAAK,cAAY,SAAS,SAAS,IAAI,CAAC,GAAG;AACrD;AAAA,MACJ;AAAA,IACJ;AAEA,WAAO,UAAU,WAAW;AAAA,EAChC;AAAA,EAEQ,gBAAgB,OAAe,OAAwB;AAC3D,UAAM,WAAW,KAAK,kBAAkB,KAAK;AAC7C,UAAM,WAAW,KAAK,kBAAkB,KAAK;AAE7C,QAAI,CAAC,YAAY,CAAC,UAAU;AACxB,aAAO;AAAA,IACX;AAGA,UAAM,aAAa,SAAS,aAAa;AAAA,MAAO,SAC5C,SAAS,aAAa,SAAS,GAAG;AAAA,IACtC;AAEA,WAAO,WAAW,SAAS;AAAA,EAC/B;AAAA,EAEO,sBAAmC;AACtC,WAAO,MAAM,KAAK,KAAK,SAAS,OAAO,CAAC,EACnC,KAAK,CAAC,GAAG,MAAM,EAAE,aAAa,EAAE,UAAU,EAC1C,MAAM,GAAG,EAAE;AAAA,EACpB;AAAA,EAEO,wBAAqC;AACxC,WAAO,MAAM,KAAK,KAAK,SAAS,OAAO,CAAC,EACnC,OAAO,aAAW,QAAQ,qBAAqB,GAAG,EAClD,KAAK,CAAC,GAAG,MAAM,EAAE,qBAAqB,EAAE,kBAAkB;AAAA,EACnE;AAAA,EAEO,wBAAqC;AACxC,WAAO,MAAM,KAAK,KAAK,SAAS,OAAO,CAAC,EACnC,OAAO,aAAW,QAAQ,mBAAmB,GAAG,EAChD,KAAK,CAAC,GAAG,MAAM,EAAE,mBAAmB,EAAE,gBAAgB;AAAA,EAC/D;AACJ;;;ACxbA,IAAAC,UAAwB;;;ACAxB,aAAwB;AACxB,IAAAC,QAAsB;AA2Bf,IAAM,cAAN,MAAkB;AAAA,EAGrB,cAAc;AACV,SAAK,gBAAuB,iBAAU,mBAAmB,IAAI,IAAI,UAAU;AAAA,EAC/E;AAAA,EAEA,MAAa,iBAA2C;AACpD,UAAM,cAAc,MAAM,KAAK,gBAAgB;AAC/C,UAAM,YAAY,KAAK,gBAAgB,WAAW;AAClD,UAAM,mBAAmB,KAAK,uBAAuB,WAAW;AAEhE,UAAM,aAAa,MAAM,KAAK,kBAAkB;AAChD,UAAM,eAAe,OAAO,KAAK,aAAa,gBAAgB,CAAC,CAAC;AAChE,UAAM,gBAAgB,MAAM,KAAK,iBAAiB;AAClD,UAAM,iBAAiB,KAAK,qBAAqB,UAAU;AAE3D,WAAO;AAAA,MACH;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,eAAe,KAAK,yBAAyB,YAAY,YAAY;AAAA,MACrE,YAAY,KAAK,oBAAoB,YAAY,YAAY;AAAA,IACjE;AAAA,EACJ;AAAA,EAEA,MAAa,YAAY,UAAiD;AACtE,QAAI;AACA,YAAM,WAAW,MAAa,iBAAU,iBAAiB,QAAQ;AACjE,YAAM,UAAU,SAAS,QAAQ;AACjC,YAAM,WAAW,SAAS;AAE1B,UAAI,CAAC,KAAK,SAAS,UAAU,OAAO,GAAG;AACnC,eAAO;AAAA,MACX;AAEA,aAAO,KAAK,eAAe,SAAS,UAAe,eAAS,QAAQ,CAAC;AAAA,IACzE,SAAS,OAAP;AACE,cAAQ,MAAM,yBAAyB,KAAK;AAC5C,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EAEA,MAAc,kBAAgC;AAC1C,QAAI;AACA,YAAM,cAAmB,WAAK,KAAK,eAAe,cAAc;AAChE,YAAM,WAAW,MAAa,iBAAU,iBAAiB,WAAW;AACpE,aAAO,KAAK,MAAM,SAAS,QAAQ,CAAC;AAAA,IACxC,QAAE;AACE,aAAO,CAAC;AAAA,IACZ;AAAA,EACJ;AAAA,EAEQ,gBAAgB,aAA0B;AAC9C,UAAM,OAAO,EAAE,GAAG,YAAY,cAAc,GAAG,YAAY,gBAAgB;AAE3E,QAAI,KAAK;AAAO,aAAO;AACvB,QAAI,KAAK;AAAK,aAAO;AACrB,QAAI,KAAK;AAAkB,aAAO;AAClC,QAAI,KAAK;AAAQ,aAAO;AACxB,QAAI,KAAK;AAAM,aAAO;AACtB,QAAI,KAAK;AAAM,aAAO;AAEtB,WAAO;AAAA,EACX;AAAA,EAEQ,uBAAuB,aAA0B;AACrD,UAAM,OAAO,EAAE,GAAG,YAAY,cAAc,GAAG,YAAY,gBAAgB;AAE3E,QAAI,KAAK,eAAe,KAAK;AAAuB,aAAO;AAC3D,QAAI,KAAK;AAAsB,aAAO;AACtC,QAAI,KAAK,qBAAqB,KAAK;AAAoB,aAAO;AAC9D,QAAI,KAAK,QAAQ,KAAK;AAAM,aAAO;AACnC,QAAI,KAAK;AAAgB,aAAO;AAChC,QAAI,KAAK;AAAkB,aAAO;AAClC,QAAI,KAAK;AAAM,aAAO;AACtB,QAAI,KAAK;AAAqB,aAAO;AAErC,WAAO;AAAA,EACX;AAAA,EAEA,MAAc,oBAA8C;AACxD,UAAM,aAA8B,CAAC;AACrC,UAAM,QAAQ,MAAa,iBAAU,UAAU,mCAAmC,oBAAoB;AAEtG,eAAW,QAAQ,MAAM,MAAM,GAAG,EAAE,GAAG;AACnC,YAAM,YAAY,MAAM,KAAK,YAAY,KAAK,MAAM;AACpD,UAAI,WAAW;AACX,mBAAW,KAAK,SAAS;AAAA,MAC7B;AAAA,IACJ;AAEA,WAAO;AAAA,EACX;AAAA,EAEA,MAAc,mBAAsC;AAChD,UAAM,QAAQ,MAAa,iBAAU,UAAU,QAAQ,oBAAoB;AAC3E,WAAO,MAAM,IAAI,UAAe,iBAAU,eAAe,IAAI,CAAC,EAAE,MAAM,GAAG,GAAG;AAAA,EAChF;AAAA,EAEQ,SAAS,UAAkB,SAA0B;AACzD,UAAM,cAAc,CAAC,cAAc,cAAc,mBAAmB,mBAAmB,OAAO,QAAQ;AACtG,QAAI,CAAC,YAAY,SAAS,QAAQ;AAAG,aAAO;AAG5C,UAAM,aAAa;AAAA,MACf;AAAA,MAAa;AAAA,MAAO;AAAA,MAAO;AAAA,MAAU;AAAA,MAAU;AAAA,MAAO;AAAA,MAAQ;AAAA,MAC9D;AAAA,MAAS;AAAA,MAAQ;AAAA,MAAa;AAAA,MAAS;AAAA,MAAS;AAAA,MAAY;AAAA,IAChE;AAEA,UAAM,eAAe,QAAQ,YAAY;AACzC,WAAO,WAAW,KAAK,aAAW,aAAa,SAAS,OAAO,CAAC;AAAA,EACpE;AAAA,EAEQ,eAAe,SAAiB,UAAkB,UAAiC;AACvF,UAAM,OAAO,KAAK,qBAAqB,SAAS,QAAQ;AACxD,UAAM,OAAO,KAAK,uBAAuB,OAAO;AAChD,UAAM,QAAQ,KAAK,aAAa,OAAO;AACvC,UAAM,UAAU,KAAK,eAAe,OAAO;AAC3C,UAAM,UAAU,KAAK,eAAe,OAAO;AAC3C,UAAM,eAAe,KAAK,oBAAoB,OAAO;AACrD,UAAM,YAAY,KAAK,2BAA2B,OAAO;AAEzD,WAAO;AAAA,MACH;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,WAAW,KAAK,UAAU,OAAO;AAAA,MACjC,cAAc,KAAK,aAAa,OAAO;AAAA,MACvC,kBAAkB,KAAK,iBAAiB,OAAO;AAAA,MAC/C,YAAY,KAAK,oBAAoB,OAAO;AAAA,IAChD;AAAA,EACJ;AAAA,EAEQ,qBAAqB,SAAiB,UAA0B;AAEpE,UAAM,gBAAgB,QAAQ,MAAM,gDAAgD;AACpF,QAAI;AAAe,aAAO,cAAc;AAGxC,WAAO,SAAS,QAAQ,6BAA6B,EAAE;AAAA,EAC3D;AAAA,EAEQ,uBAAuB,SAAwC;AACnE,QAAI,QAAQ,SAAS,QAAQ,KAAK,QAAQ,SAAS,SAAS;AAAG,aAAO;AACtE,QAAI,QAAQ,SAAS,KAAK,KAAK,QAAQ,MAAM,UAAU;AAAG,aAAO;AACjE,QAAI,QAAQ,SAAS,UAAU,KAAK,QAAQ,SAAS,OAAO,KAAK,QAAQ,SAAS,IAAI;AAAG,aAAO;AAChG,WAAO;AAAA,EACX;AAAA,EAEQ,aAAa,SAA2B;AAC5C,UAAM,QAAkB,CAAC;AAGzB,UAAM,iBAAiB,QAAQ,MAAM,mCAAmC;AACxE,QAAI,gBAAgB;AAChB,YAAM,eAAe,eAAe;AACpC,YAAM,cAAc,aAAa,MAAM,gBAAgB;AACvD,UAAI,aAAa;AACb,cAAM,KAAK,GAAG,YAAY,IAAI,WAAS,MAAM,QAAQ,SAAS,EAAE,CAAC,CAAC;AAAA,MACtE;AAAA,IACJ;AAGA,UAAM,mBAAmB,QAAQ,MAAM,iBAAiB;AACxD,QAAI,kBAAkB;AAClB,YAAM,eAAe,iBAAiB,GAAG,MAAM,GAAG,EAAE,IAAI,UAAQ,KAAK,KAAK,EAAE,MAAM,GAAG,EAAE,GAAG,KAAK,CAAC;AAChG,YAAM,KAAK,GAAG,YAAY;AAAA,IAC9B;AAEA,WAAO,CAAC,GAAG,IAAI,IAAI,KAAK,CAAC;AAAA,EAC7B;AAAA,EAEQ,eAAe,SAA2B;AAC9C,UAAM,UAAoB,CAAC;AAC3B,UAAM,gBAAgB,QAAQ,MAAM,sCAAsC;AAE1E,QAAI,eAAe;AACf,iBAAW,SAAS,eAAe;AAC/B,cAAM,cAAc,MAAM,MAAM,yBAAyB;AACzD,YAAI,aAAa;AACb,kBAAQ,KAAK,YAAY,EAAE;AAAA,QAC/B;AAAA,MACJ;AAAA,IACJ;AAEA,WAAO;AAAA,EACX;AAAA,EAEQ,eAAe,SAA2B;AAC9C,UAAM,UAAoB,CAAC;AAC3B,UAAM,gBAAgB,QAAQ,MAAM,0EAA0E;AAE9G,QAAI,eAAe;AACf,iBAAW,SAAS,eAAe;AAC/B,cAAM,YAAY,MAAM,MAAM,iDAAiD;AAC/E,YAAI,WAAW;AACX,kBAAQ,KAAK,UAAU,EAAE;AAAA,QAC7B;AAAA,MACJ;AAAA,IACJ;AAEA,WAAO;AAAA,EACX;AAAA,EAEQ,oBAAoB,SAA2B;AACnD,UAAM,OAAiB,CAAC;AACxB,UAAM,UAAU,KAAK,eAAe,OAAO;AAE3C,eAAW,OAAO,SAAS;AACvB,UAAI,CAAC,IAAI,WAAW,GAAG,KAAK,CAAC,IAAI,WAAW,GAAG,GAAG;AAC9C,aAAK,KAAK,IAAI,MAAM,GAAG,EAAE,EAAE;AAAA,MAC/B;AAAA,IACJ;AAEA,WAAO,CAAC,GAAG,IAAI,IAAI,IAAI,CAAC;AAAA,EAC5B;AAAA,EAEQ,2BAA2B,SAAyB;AACxD,QAAI,QAAQ,SAAS,OAAO,KAAK,QAAQ,SAAS,KAAK,KAAK,QAAQ,SAAS,UAAU;AAAG,aAAO;AACjG,QAAI,QAAQ,SAAS,KAAK,KAAK,QAAQ,SAAS,YAAY;AAAG,aAAO;AACtE,QAAI,QAAQ,SAAS,YAAY,KAAK,QAAQ,SAAS,SAAS;AAAG,aAAO;AAC1E,QAAI,QAAQ,SAAS,QAAQ,KAAK,QAAQ,SAAS,UAAU;AAAG,aAAO;AACvE,WAAO;AAAA,EACX;AAAA,EAEQ,UAAU,SAA0B;AACxC,WAAO,QAAQ,SAAS,WAAW,KAC5B,QAAQ,SAAS,QAAQ,KACzB,QAAQ,SAAS,QAAQ,KACzB,QAAQ,SAAS,MAAM,KACvB,QAAQ,SAAS,aAAa;AAAA,EACzC;AAAA,EAEQ,aAAa,SAA0B;AAC3C,UAAM,qBAAqB,CAAC,cAAc,UAAU,UAAU,WAAW,cAAc,SAAS,OAAO,OAAO,OAAO,KAAK;AAC1H,WAAO,mBAAmB,KAAK,aAAW,QAAQ,YAAY,EAAE,SAAS,OAAO,CAAC;AAAA,EACrF;AAAA,EAEQ,iBAAiB,SAA0B;AAC/C,UAAM,eAAe,CAAC,SAAS,SAAS,YAAY,QAAQ,SAAS,iBAAiB,eAAe;AACrG,WAAO,aAAa,KAAK,aAAW,QAAQ,YAAY,EAAE,SAAS,OAAO,CAAC;AAAA,EAC/E;AAAA,EAEQ,oBAAoB,SAA8C;AACtE,UAAM,QAAQ,QAAQ,MAAM,IAAI,EAAE;AAClC,UAAM,wBAAwB,QAAQ,MAAM,2CAA2C,KAAK,CAAC,GAAG;AAEhG,QAAI,QAAQ,OAAO,uBAAuB;AAAI,aAAO;AACrD,QAAI,QAAQ,OAAO,uBAAuB;AAAG,aAAO;AACpD,WAAO;AAAA,EACX;AAAA,EAEQ,qBAAqB,YAAuC;AAChE,UAAM,WAAqB,CAAC;AAG5B,QAAI,WAAW,KAAK,OAAK,EAAE,KAAK,SAAS,UAAU,CAAC;AAAG,eAAS,KAAK,kBAAkB;AACvF,QAAI,WAAW,KAAK,OAAK,EAAE,KAAK,SAAS,MAAM,CAAC;AAAG,eAAS,KAAK,cAAc;AAC/E,QAAI,WAAW,KAAK,OAAK,EAAE,KAAK,SAAS,KAAK,CAAC;AAAG,eAAS,KAAK,yBAAyB;AACzF,QAAI,WAAW,KAAK,OAAK,EAAE,KAAK,SAAS,WAAW,CAAC;AAAG,eAAS,KAAK,0BAA0B;AAEhG,WAAO;AAAA,EACX;AAAA,EAEQ,yBAAyB,YAA6B,cAAiC;AAC3F,WAAO,WAAW,KAAK,OAAK,EAAE,gBAAgB,KACvC,aAAa,KAAK,SAAO,IAAI,SAAS,MAAM,KAAK,IAAI,SAAS,eAAe,CAAC;AAAA,EACzF;AAAA,EAEQ,oBAAoB,YAA6B,cAAiC;AACtF,WAAO,WAAW,KAAK,OAAK,EAAE,YAAY,KACnC,aAAa,SAAS,aAAa,KACnC,aAAa,SAAS,QAAQ;AAAA,EACzC;AACJ;;;AD5SO,IAAM,cAAN,MAAkB;AAAA,EAOrB,YACY,kBACR,UACF;AAFU;AAPZ,SAAQ,WAAuC,CAAC;AAGhD,SAAQ,cAAiC,CAAC;AAC1C,SAAQ,kBAAyC;AAM7C,SAAK,WAAW;AAChB,SAAK,cAAc,IAAI,YAAY;AACnC,SAAK,mBAAmB;AAAA,EAC5B;AAAA,EAEQ,qBAA2B;AAE/B,UAAM,WAAW;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAEA,eAAW,WAAW,UAAU;AAC5B,YAAM,UAAiB,kBAAU,wBAAwB,OAAO;AAEhE,cAAQ,YAAY,SAAO,KAAK,iBAAiB,WAAW,IAAI,MAAM,CAAC;AACvE,cAAQ,YAAY,SAAO,KAAK,iBAAiB,YAAY,IAAI,MAAM,CAAC;AACxE,cAAQ,YAAY,SAAO,KAAK,iBAAiB,WAAW,IAAI,MAAM,CAAC;AAEvE,WAAK,SAAS,KAAK,OAAO;AAAA,IAC9B;AAGA,IAAO,eAAO,4BAA4B,YAAU;AAChD,UAAI,UAAU,KAAK,SAAS,OAAO,QAAQ,GAAG;AAC1C,aAAK,uBAAuB,OAAO,QAAQ;AAAA,MAC/C;AAAA,IACJ,CAAC;AAGD,IAAO,kBAAU,sBAAsB,cAAY;AAC/C,UAAI,KAAK,SAAS,QAAQ,GAAG;AACzB,aAAK,iBAAiB,YAAY,SAAS,IAAI,MAAM;AAAA,MACzD;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EAEQ,iBAAiB,MAA+B,UAAwB;AAE5E,QAAI,KAAK,iBAAiB,QAAQ,GAAG;AACjC;AAAA,IACJ;AAEA,UAAM,QAAyB;AAAA,MAC3B;AAAA,MACA;AAAA,MACA,WAAW,IAAI,KAAK;AAAA,IACxB;AAEA,SAAK,YAAY,KAAK,KAAK;AAC3B,SAAK,mBAAmB;AAAA,EAC5B;AAAA,EAEQ,qBAA2B;AAE/B,QAAI,KAAK,iBAAiB;AACtB,mBAAa,KAAK,eAAe;AAAA,IACrC;AAEA,SAAK,kBAAkB,WAAW,MAAM;AACpC,WAAK,mBAAmB;AAAA,IAC5B,GAAG,GAAI;AAAA,EACX;AAAA,EAEA,MAAc,qBAAoC;AAC9C,QAAI,KAAK,YAAY,WAAW;AAAG;AAEnC,UAAM,SAAS,CAAC,GAAG,KAAK,WAAW;AACnC,SAAK,cAAc,CAAC;AAEpB,eAAW,SAAS,QAAQ;AACxB,UAAI;AACA,cAAM,KAAK,kBAAkB,KAAK;AAAA,MACtC,SAAS,OAAP;AACE,gBAAQ,MAAM,oCAAoC,MAAM,aAAa,KAAK;AAAA,MAC9E;AAAA,IACJ;AAAA,EACJ;AAAA,EAEA,MAAc,kBAAkB,OAAuC;AACnE,YAAQ,MAAM,MAAM;AAAA,MAChB,KAAK;AAAA,MACL,KAAK;AACD,cAAM,KAAK,kBAAkB,MAAM,QAAQ;AAC3C;AAAA,MACJ,KAAK;AACD,cAAM,KAAK,kBAAkB,MAAM,QAAQ;AAC3C;AAAA,IACR;AAAA,EACJ;AAAA,EAEA,MAAc,kBAAkB,UAAiC;AAC7D,QAAI;AACA,YAAM,gBAAgB,MAAM,KAAK,YAAY,YAAY,QAAQ;AACjE,UAAI,CAAC;AAAe;AAEpB,YAAM,WAAW,MAAa,kBAAU,iBAAiB,QAAQ;AACjE,YAAM,UAAU,SAAS,QAAQ;AAEjC,YAAM,UAAsC;AAAA,QACxC;AAAA,QACA,SAAS,QAAQ,UAAU,GAAG,GAAK;AAAA,QACnC,UAAU,SAAS;AAAA,QACnB,WAAW,cAAc;AAAA,QACzB,cAAc,cAAc;AAAA,QAC5B,SAAS,cAAc;AAAA,QACvB,SAAS,cAAc;AAAA,QACvB,YAAY,CAAC,cAAc,IAAI;AAAA,QAC/B,WAAW,IAAI,aAAa,GAAG;AAAA,QAC/B,gBAAgB,CAAC;AAAA,QACjB,cAAc,IAAI,KAAK;AAAA,MAC3B;AAGA,YAAM,kBAAkB,KAAK,SAAS,kBAAkB,QAAQ;AAChE,UAAI,iBAAiB;AAEjB,eAAO,OAAO,iBAAiB,OAAO;AAAA,MAC1C,OAAO;AAEH,aAAK,SAAS,kBAAkB,OAAO;AAAA,MAC3C;AAAA,IAEJ,SAAS,OAAP;AACE,cAAQ,MAAM,8BAA8B,aAAa,KAAK;AAAA,IAClE;AAAA,EACJ;AAAA,EAEA,MAAc,kBAAkB,UAAiC;AAG7D,YAAQ,IAAI,iBAAiB,UAAU;AAAA,EAC3C;AAAA,EAEA,MAAc,uBAAuB,UAA8C;AAE/E,QAAI,KAAK,SAAS,QAAQ,GAAG;AACzB,YAAM,KAAK,kBAAkB,SAAS,IAAI,MAAM;AAAA,IACpD;AAAA,EACJ;AAAA,EAEQ,SAAS,UAAwC;AACrD,UAAM,eAAe,CAAC,QAAQ,QAAQ,OAAO,OAAO,QAAQ,SAAS;AACrE,UAAM,cAAc,CAAC,cAAc,cAAc,mBAAmB,mBAAmB,OAAO,QAAQ;AAEtG,WAAO,aAAa,KAAK,SAAO,SAAS,SAAS,SAAS,GAAG,CAAC,KACxD,YAAY,SAAS,SAAS,UAAU;AAAA,EACnD;AAAA,EAEQ,iBAAiB,UAA2B;AAChD,UAAM,cAAc;AAAA,MAChB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAEA,WAAO,YAAY,KAAK,YAAU,SAAS,SAAS,MAAM,CAAC;AAAA,EAC/D;AAAA,EAEO,iBAAiB,QAAgB,IAAuB;AAC3D,WAAO,KAAK,YACP,KAAK,CAAC,GAAG,MAAM,EAAE,UAAU,QAAQ,IAAI,EAAE,UAAU,QAAQ,CAAC,EAC5D,MAAM,GAAG,KAAK;AAAA,EACvB;AAAA,EAEA,MAAa,oBAKV;AACC,UAAM,kBAAkB,MAAM,KAAK,YAAY,eAAe;AAC9D,UAAM,gBAAgB,KAAK,YAAY;AAAA,MACnC,WAAS,MAAM,YAAY,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,GAAI;AAAA,IACxE,EAAE;AAEF,WAAO;AAAA,MACH,YAAY,gBAAgB,cAAc;AAAA,MAC1C;AAAA,MACA,YAAY,CAAC,gBAAgB,WAAW,gBAAgB,gBAAgB;AAAA,MACxE,YAAY,gBAAgB,WAAW;AAAA,IAC3C;AAAA,EACJ;AAAA,EAEA,MAAa,2BAIV;AACC,UAAM,eAAsB,eAAO;AACnC,QAAI,CAAC,cAAc;AACf,aAAO,EAAE,cAAc,CAAC,GAAG,aAAa,CAAC,EAAE;AAAA,IAC/C;AAEA,UAAM,cAAc,KAAK,SAAS,kBAAkB,aAAa,SAAS,IAAI,MAAM;AACpF,UAAM,eAAiC,CAAC;AACxC,UAAM,cAAwB,CAAC;AAE/B,QAAI,aAAa;AAGb,kBAAY;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AAEA,WAAO;AAAA,MACH;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA,EAEO,UAAgB;AAEnB,eAAW,WAAW,KAAK,UAAU;AACjC,cAAQ,QAAQ;AAAA,IACpB;AACA,SAAK,WAAW,CAAC;AAGjB,QAAI,KAAK,iBAAiB;AACtB,mBAAa,KAAK,eAAe;AACjC,WAAK,kBAAkB;AAAA,IAC3B;AAAA,EACJ;AACJ;;;AF7PO,IAAM,2BAAN,MAAqE;AAAA,EAQxE,YAAoB,SAAkC;AAAlC;AAHpB,SAAQ,WAA6B,CAAC;AACtC,SAAQ,eAAwB;AAG5B,SAAK,WAAW,IAAI,eAAe,OAAO;AAC1C,SAAK,cAAc,IAAI,YAAY,SAAS,KAAK,QAAQ;AACzD,SAAK,cAAc,IAAI,YAAY;AAAA,EACvC;AAAA,EAEO,mBACH,aACA,UACA,QACI;AACJ,SAAK,UAAU;AAEf,gBAAY,QAAQ,UAAU;AAAA,MAC1B,eAAe;AAAA,MACf,oBAAoB,CAAC,KAAK,QAAQ,YAAY;AAAA,IAClD;AAEA,gBAAY,QAAQ,OAAO,KAAK,kBAAkB;AAGlD,gBAAY,QAAQ,oBAAoB,OAAO,YAAY;AACvD,YAAM,KAAK,qBAAqB,OAAO;AAAA,IAC3C,CAAC;AAGD,SAAK,mBAAmB;AAAA,EAC5B;AAAA,EAEA,MAAc,qBAAoC;AAC9C,QAAI;AAEA,YAAM,KAAK,YAAY,MAAM;AAE7B,WAAK,WAAW;AAAA,QACZ,MAAM;AAAA,QACN,SAAS;AAAA,QACT,WAAW,IAAI,KAAK;AAAA,MACxB,CAAC;AAAA,IACL,SAAS,OAAP;AACE,WAAK,WAAW;AAAA,QACZ,MAAM;AAAA,QACN,SAAS,iCAAiC;AAAA,QAC1C,WAAW,IAAI,KAAK;AAAA,MACxB,CAAC;AAAA,IACL;AAAA,EACJ;AAAA,EAEA,MAAc,qBAAqB,SAA6B;AAC5D,YAAQ,QAAQ,MAAM;AAAA,MAClB,KAAK;AACD,cAAM,KAAK,gBAAgB,QAAQ,OAAO;AAC1C;AAAA,MACJ,KAAK;AACD,cAAM,KAAK,mBAAmB;AAC9B;AAAA,MACJ,KAAK;AACD,aAAK,kBAAkB;AACvB;AAAA,MACJ;AACI,gBAAQ,IAAI,yBAAyB,QAAQ,IAAI;AAAA,IACzD;AAAA,EACJ;AAAA,EAEA,MAAc,gBAAgB,QAA+B;AACzD,QAAI,KAAK,cAAc;AACnB,WAAK,WAAW;AAAA,QACZ,MAAM;AAAA,QACN,SAAS;AAAA,QACT,WAAW,IAAI,KAAK;AAAA,MACxB,CAAC;AACD;AAAA,IACJ;AAEA,SAAK,eAAe;AAEpB,SAAK,WAAW;AAAA,MACZ,MAAM;AAAA,MACN,SAAS;AAAA,MACT,WAAW,IAAI,KAAK;AAAA,IACxB,CAAC;AAED,QAAI;AAEA,YAAM,UAAU,MAAM,KAAK,mBAAmB,MAAM;AAGpD,YAAM,WAAW,MAAM,KAAK,mBAAmB,QAAQ,OAAO;AAE9D,WAAK,WAAW;AAAA,QACZ,MAAM;AAAA,QACN,SAAS;AAAA,QACT,WAAW,IAAI,KAAK;AAAA,QACpB;AAAA,MACJ,CAAC;AAAA,IAEL,SAAS,OAAP;AACE,WAAK,WAAW;AAAA,QACZ,MAAM;AAAA,QACN,SAAS,wBAAwB;AAAA,QACjC,WAAW,IAAI,KAAK;AAAA,MACxB,CAAC;AAAA,IACL,UAAE;AACE,WAAK,eAAe;AAAA,IACxB;AAAA,EACJ;AAAA,EAEA,MAAc,mBAAmB,QAA8B;AAE3D,UAAM,gBAAgB,MAAM,KAAK,SAAS,eAAe,QAAQ,CAAC;AAGlE,UAAM,eAAsB,eAAO;AACnC,UAAM,qBAAqB,eACrB,KAAK,SAAS,kBAAkB,aAAa,SAAS,IAAI,MAAM,IAChE;AAGN,UAAM,kBAAkB,MAAM,KAAK,YAAY,eAAe;AAE9D,WAAO;AAAA,MACH;AAAA,MACA,aAAa;AAAA,MACb,SAAS;AAAA,QACL,WAAW,gBAAgB;AAAA,QAC3B,kBAAkB,gBAAgB;AAAA,QAClC,kBAAkB,gBAAgB;AAAA,QAClC,cAAc,gBAAgB;AAAA,MAClC;AAAA,IACJ;AAAA,EACJ;AAAA,EAEA,MAAc,mBAAmB,QAAgB,SAA+B;AAE5E,UAAM,SAAgB,kBAAU,iBAAiB,SAAS,EAAE,IAAY,QAAQ;AAEhF,QAAI,CAAC,QAAQ;AACT,aAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,eAYJ,QAAQ,QAAQ;AAAA,aAClB,QAAQ,QAAQ;AAAA,mBACV,QAAQ,QAAQ,mBAAmB,WAAM;AAAA,gBAC5C,QAAQ,QAAQ,eAAe,WAAM;AAAA,6BACxB,QAAQ,cAAc;AAAA;AAAA,oBAE/B;AAAA;AAAA,IAEZ;AAGA,WAAO;AAAA;AAAA;AAAA;AAAA,YAIH,KAAK,qBAAqB,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,eAK7B,KAAK,qBAAqB,MAAM,eAAe,KAAK,qBAAqB,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,eAa/E,QAAQ,QAAQ;AAAA,aAClB,QAAQ,QAAQ;AAAA,UACnB,QAAQ,cAAc;AAAA;AAAA;AAAA;AAAA,EAI5B;AAAA,EAEQ,qBAAqB,QAAwB;AAEjD,UAAM,QAAQ,OAAO,MAAM,GAAG,EAAE,OAAO,UAAQ,KAAK,SAAS,CAAC;AAC9D,WAAO,MAAM,SAAS,IAAI,MAAM,GAAG,OAAO,CAAC,EAAE,YAAY,IAAI,MAAM,GAAG,MAAM,CAAC,IAAI;AAAA,EACrF;AAAA,EAEA,MAAc,qBAAoC;AAC9C,UAAM,SAAS,KAAK,YAAY,kBAAkB;AAClD,UAAM,mBAAmB,KAAK,SAAS,oBAAoB;AAE3D,SAAK,SAAS,QAAQ,YAAY;AAAA,MAC9B,MAAM;AAAA,MACN,MAAM;AAAA,QACF,gBAAgB;AAAA,QAChB,kBAAkB,iBAAiB,MAAM,GAAG,CAAC;AAAA,QAC7C,eAAe,iBAAiB;AAAA,MACpC;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EAEQ,WAAW,SAA+B;AAC9C,SAAK,SAAS,KAAK,OAAO;AAC1B,SAAK,cAAc;AAAA,EACvB;AAAA,EAEQ,oBAA0B;AAC9B,SAAK,WAAW,CAAC;AACjB,SAAK,cAAc;AAAA,EACvB;AAAA,EAEQ,gBAAsB;AAC1B,QAAI,KAAK,SAAS;AACd,WAAK,QAAQ,QAAQ,YAAY;AAAA,QAC7B,MAAM;AAAA,QACN,UAAU,KAAK;AAAA,MACnB,CAAC;AAAA,IACL;AAAA,EACJ;AAAA,EAEQ,oBAA4B;AAChC,WAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqOX;AAAA,EAEO,UAAgB;AACnB,SAAK,YAAY,QAAQ;AAAA,EAC7B;AACJ;;;ADneA,IAAM,eAAe,CAAC,cAAc,kBAAkB,oBAAoB,cAAc,iBAAiB,oBAAoB,qBAAqB,oBAAoB,gBAAgB,gBAAgB;AAEtM,eAAsB,SAAS,SAAkC;AAChE,UAAQ,IAAI,oCAAoC;AAEhD,QAAM,WAAW,IAAI,yBAAyB,OAAO;AAErD,QAAM,OAAc,eAAO;AAAA,IAC1B;AAAA,IACA;AAAA,IACA;AAAA,MACC,gBAAgB;AAAA,QACf,yBAAyB;AAAA,MAC1B;AAAA,IACD;AAAA,EACD;AAEA,QAAM,WAAkB,iBAAS,gBAAgB,oBAAoB,YAAY;AAChF,UAAM,QAAQ,MAAa,eAAO,aAAa;AAAA,MAC9C,QAAQ;AAAA,MACR,aAAa;AAAA,IACd,CAAC;AAED,QAAI,OAAO;AAEV,MAAO,iBAAS,eAAe,oBAAoB;AAAA,IACpD;AAAA,EACD,CAAC;AAED,QAAM,oBAA2B,iBAAS,gBAAgB,6BAA6B,YAAY;AAElG,IAAO,iBAAS,eAAe,oBAAoB;AAAA,EACpD,CAAC;AAGD,QAAM,gBAAuB,kBAAU,yBAAyB,OAAK;AACpE,QAAI,EAAE,qBAAqB,gBAAgB,KAAK,EAAE,qBAAqB,6BAA6B,GAAG;AAEtG,cAAQ,IAAI,+BAA+B;AAAA,IAC5C;AAAA,EACD,CAAC;AAED,QAAM,wBAA+B,iBAAS,gBAAgB,0BAA0B,YAAY;AACnG,UAAM,SAAgB,eAAO;AAE7B,QAAI,CAAC,QAAQ;AACZ;AAAA,IACD;AAEA,UAAM,YAAY,OAAO,SAAS,QAAQ,OAAO,SAAS;AAC1D,QAAI,YAAY;AAChB,QAAI,WAAW;AACd,YAAa,eACX,aAAa;AAAA,QACb,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,gBAAgB;AAAA,QAChB,aAAa;AAAA,QACb,OAAO;AAAA,MACR,CAAC,EACA,KAAK,CAAC,UAAU;AAChB,YAAI,CAAC,OAAO;AACX,sBAAY;AACZ;AAAA,QACD;AAEA,+BAAuB,MAAM,KAAK,KAAK;AACvC,gBAAQ,YAAY,OAAO,yBAAyB,oBAAoB;AAAA,MACzE,CAAC;AAEF,UAAI,CAAC,aAAa,sBAAsB,SAAS,GAAG;AACnD,kBAAU,eAAe,sBAAsB,EAAE,SAAS,kBAAkB,MAAM,UAAU,CAAC;AAAA,MAC9F;AAAA,IACD;AAAA,EACD,CAAC;AAED,QAAM,2BAAkC,iBAAS,gBAAgB,6BAA6B,MAAM;AACnG,UAAM,SAAgB,eAAO;AAE7B,QAAI,CAAC,QAAQ;AACZ;AAAA,IACD;AAEA,UAAM,YAAY,OAAO,SAAS,QAAQ,OAAO,SAAS;AAC1D,QAAI,WAAW;AACd,gBAAU,eAAe,WAAW,EAAE,SAAS,qBAAqB,UAAU,OAAO,SAAS,WAAW,CAAC;AAAA,IAC3G;AAAA,EACD,CAAC;AAGD,QAAM,qBAAqB,aAAa,OAAO,aAAW,YAAY,oBAAoB,YAAY,mBAAmB,EAAE,IAAI,CAAC,YAAmB,iBAAS,gBAAgB,WAAW,WAAW,MAAM;AACvM,UAAM,SAAgB,kBAAU,iBAAiB,SAAS,EAAE,IAAY,gBAAgB,SAAS;AACjG,UAAM,SAAgB,eAAO;AAE7B,QAAI,CAAC,QAAQ;AACZ;AAAA,IACD;AAEA,UAAM,YAAY,OAAO,SAAS,QAAQ,OAAO,SAAS;AAC1D,QAAI,aAAa,QAAQ;AACxB,gBAAU,eAAe,QAAQ,EAAE,SAAS,MAAM,WAAW,UAAU,OAAO,SAAS,WAAW,CAAC;AAAA,IACpG;AAAA,EACD,CAAC,CAAC;AAGF,QAAM,gBAAuB,iBAAS,gBAAgB,yBAAyB,YAAY;AAC1F,UAAM,UAAU,wBAAwB,QAAQ;AAAA,EACjD,CAAC;AAED,QAAM,aAAoB,iBAAS,gBAAgB,sBAAsB,YAAY;AACpF,UAAM,UAAU,wBAAwB,KAAK;AAAA,EAC9C,CAAC;AAED,QAAM,cAAqB,iBAAS,gBAAgB,uBAAuB,YAAY;AACtF,UAAM,UAAU,wBAAwB,MAAM;AAAA,EAC/C,CAAC;AAED,QAAM,gBAAuB,iBAAS,gBAAgB,yBAAyB,YAAY;AAC1F,UAAM,UAAU,wBAAwB,QAAQ;AAAA,EACjD,CAAC;AAED,QAAM,uBAA8B,iBAAS,gBAAgB,gCAAgC,YAAY;AACxG,UAAM,UAAU,qBAAqB;AAAA,EACtC,CAAC;AAED,QAAM,0BAAiC,iBAAS,gBAAgB,mCAAmC,MAAM;AACxG,cAAU,wBAAwB;AAAA,EACnC,CAAC;AAED,QAAM,qBAA4B,iBAAS,gBAAgB,8BAA8B,YAAY;AACpG,UAAM,UAAU,mBAAmB;AAAA,EACpC,CAAC;AAED,QAAM,yBAAgC,iBAAS,gBAAgB,kCAAkC,MAAM;AACtG,cAAU,uBAAuB;AAAA,EAClC,CAAC;AAED,UAAQ,cAAc,KAAK,MAAM,UAAU,aAAa,oBAAoB,cAAc,eAAe,uBAAuB,0BAA0B,GAAG,oBAAoB,eAAe,YAAY,aAAa,eAAe,sBAAsB,yBAAyB,oBAAoB,sBAAsB;AAEjU,QAAM,aAAa,MAAM;AACxB,iBAAa,QAAQ,aAAW;AAC/B,UAAI,YAAY,qBAAqB;AACpC,YAAI,2BAA2B,CAAC,CAAQ,kBAAU,iBAAiB,SAAS,EAAE,IAAa,2BAA2B;AACtH,QAAO,iBAAS,eAAe,cAAc,6BAA6B,wBAAwB;AAAA,MACnG,OAAO;AACN,cAAM,UAAU,CAAC,CAAQ,kBAAU,iBAAiB,sBAAsB,EAAE,IAAa,GAAG,iBAAiB;AAC7G,QAAO,iBAAS,eAAe,cAAc,GAAG,mBAAmB,OAAO;AAAA,MAC3E;AAAA,IACD,CAAC;AAAA,EACF;AAEA,aAAW;AACZ;AAEO,SAAS,aAAa;AAG7B;", "names": ["vscode", "vscode", "vscode", "path"]}